/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryRecordParam
 * Description:AISummaryRecordParam
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import com.oplus.unified.summary.sdk.speech.DialogContent

data class AISummaryRecordParam(
    val mediaID: Long,
    val sessionId: String,
    val timeout: Long = TIMEOUT,
    val inputLanguage: String = LANGUAGE_DEFAULT,
    val outputLanguage: String = LANGUAGE_DEFAULT,
    val asrContent: String,
    val dialogContent: List<DialogContent>,
    val length: Int,
    val isOverSize: Boolean = false,
    val isPhoneRecord: Boolean = false
) {
    companion object {
        const val TIMEOUT = 5 * 60 * 1000L
        const val LANGUAGE_DEFAULT = "zh"
    }

    override fun toString(): String {
        return "ConvertTitleParam(sessionId=$sessionId, content=$asrContent," +
                "inputLanguage=$inputLanguage, outputLanguage:$outputLanguage, length = $length)"
    }
}