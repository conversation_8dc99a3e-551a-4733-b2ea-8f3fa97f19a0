/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File       : AISummaryServiceHelper.kt
 ** Description: AISummaryServiceHelper.kt
 ** Version    : 1.0
 ** Date       : 2025/06/23
 ** Author     : <EMAIL>
 * <p>
 ** ---------------------Revision History: ---------------------
 **  <author>   <data>   <version >    <desc>
 **  W9017070  2025/06/23      1.0     create file
 ****************************************************************/
package com.soundrecorder.summary.util

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.model.SummaryRequestModel
import com.soundrecorder.summary.request.AISummaryProcess.Companion.STOP_REASON
import com.soundrecorder.summary.request.AISummaryService
import com.soundrecorder.summary.request.AISummaryServiceBinder
import com.soundrecorder.summary.request.IAISummaryCallback

object StartAISummaryServiceHelper {
    private const val TAG = "StartAISummaryServiceHelper"

    //Service相关
    private var startBind = false
    private var summaryService: AISummaryService? = null
    private lateinit var summaryRequestModel: SummaryRequestModel
    private lateinit var aiSummaryCallback: IAISummaryCallback
    private lateinit var serviceConnection: SummaryConnection

    fun initCallBack(
        startCallBack: () -> Unit,
        errorCallBack: () -> Unit,
        endCallBack: () -> Unit
    ) {
        // AI摘要回调
        aiSummaryCallback = object : IAISummaryCallback {

            override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onAISummaryStart: mediaId=$mediaId")
                startCallBack.invoke()
            }

            override fun onAISummaryDataAvailable(
                mediaId: Long,
                stream: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onAISummaryDataAvailable: mediaId=$mediaId")
            }

            override fun onAISummaryFinished(
                mediaId: Long,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onAISummaryFinished: mediaId=$mediaId")
                endCallBack.invoke()
            }

            override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
                DebugUtil.e(
                    TAG,
                    "onAISummaryError: mediaId=$mediaId,errorCode=$errorCode,errorMsg=$errorMsg"
                )
                errorCallBack.invoke()
            }

            override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
                val reason = (extras?.get(STOP_REASON) as? Int) ?: -1
                DebugUtil.d(TAG, "onAISummaryStop: mediaId=$mediaId, reason=$reason")
                serviceConnection.onServiceDisconnected(
                    ComponentName(
                        BaseApplication.getAppContext(),
                        AISummaryService::class.java
                    )
                )
                release()
            }

            override fun onAISummaryWait(mediaId: Long) {
                DebugUtil.d(TAG, "onAISummaryWait: mediaId=$mediaId")
            }
        }
    }

    fun startAISummary(
        mediaId: Long,
        recordType: Int,
        recordTime: Long,
        startCallBack: () -> Unit,
        errorCallBack: () -> Unit,
        endCallBack: () -> Unit
    ) {
        DebugUtil.d(TAG, "startAISummary start")
        initCallBack(startCallBack, errorCallBack, endCallBack)
        summaryRequestModel = SummaryRequestModel(mediaId, recordType, recordTime)
        startSummaryService()
        bindService(summaryRequestModel)
    }

    private fun startSummaryService() {
        val serviceIntent = Intent(BaseApplication.getAppContext(), AISummaryService::class.java)
        serviceIntent.`package` = BaseApplication.getAppContext().packageName
        BaseApplication.getAppContext().startService(serviceIntent)
    }

    private fun bindService(summaryRequestModel: SummaryRequestModel): Boolean {
        DebugUtil.d(TAG, "bindService startBind=$startBind")
        if (!startBind) {
            startBind = true
            val serviceIntent = Intent(BaseApplication.getAppContext(), AISummaryService::class.java)
            serviceIntent.`package` = BaseApplication.getAppContext().packageName
            serviceConnection = SummaryConnection(summaryRequestModel, aiSummaryCallback)
            return BaseApplication.getAppContext().bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
        return false
    }

    fun release() {
        summaryService?.unregisterAISummaryCallback(
            summaryRequestModel.mediaId,
            aiSummaryCallback
        )
        summaryService = null
    }

    class SummaryConnection(
        private val model: SummaryRequestModel,
        private val callback: IAISummaryCallback
    ) : ServiceConnection {
        companion object {
            private const val TAG = "SummaryConnection"
        }

        override fun onServiceConnected(name: ComponentName?, binder: IBinder?) {
            val service = (binder as? AISummaryServiceBinder)?.service ?: run {
                DebugUtil.e(TAG, "onServiceConnected but service is null")
                return
            }
            DebugUtil.d(TAG, "onServiceConnected")
            summaryService = service
            service.registerAISummaryCallback(model.mediaId, callback)
            service.startAISummary(model)
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            DebugUtil.d(TAG, "onServiceDisconnected")
            startBind = false
        }
    }
}