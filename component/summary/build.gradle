apply from: "../../common_build.gradle"

android {
    namespace 'com.soundrecorder.summary'
    buildFeatures {
        aidl true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(path: ':common:libbase')
    implementation project(path: ':common:libcommon')
    implementation project(path: ':common:RecorderLogBase')
    implementation project(':common:modulerouter')
    implementation project(':component:translate')
    implementation project(':component:share')
    implementation project(':common:markwon-core')
    implementation project(':common:markwon-ext-tasklist')
    // Koin for Android
    implementation(libs.koin)
    implementation libs.androidx.core.ktx
    implementation libs.gson
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation libs.oplus.aiunit.core
    implementation(libs.oplus.aiunit.toolkit)
    implementation(libs.oplus.aiunit.download)
    implementation(libs.oplus.aiunit.common)
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.scrollview
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.snackbar
    implementation libs.oplus.coui.tips
    implementation libs.oplus.coui.cardview
    //ai-sdk
    implementation libs.ai.unified.summary
    //markwon
    implementation libs.common.mark

    // Room
    kapt libs.androidx.room.compiler
    implementation libs.androidx.room.runtime

    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
}