/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ConvertSupportApi
 * Description:
 * Version: 1.0
 * Date: 2025/3/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/12 1.0 create
 */

package com.soundrecorder.convertservice.api

import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction

object ConvertSupportApi : ConvertSupportAction {

    override fun isSupportConvert(fromMainProcess: Boolean): Boolean {
        return ConvertSupportManager.isSupportConvert(fromMainProcess)
    }

    override fun getConvertSupportType(fromMainProcess: Boolean): Int {
        return ConvertSupportManager.getConvertSupportType(fromMainProcess)
    }
}