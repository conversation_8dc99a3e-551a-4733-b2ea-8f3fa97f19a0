/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MergeContentHelperTest.kt
 ** Description : MergeContentHelperTest.kt
 ** Version     : 1.0
 ** Date        : 2025/06/25
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/06/25      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.asr.realtime

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.ASR_RESULT_TYPE_INTERMEDIATE
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.ASR_RESULT_TYPE_VAD_FINAL
import com.soundrecorder.translate.shadow.ShadowFeatureOption
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import kotlin.random.Random

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MergeContentHelperTest {
    private lateinit var mergeContentHelper: MergeContentHelper
    private var mExpectedWord85: Double = 0.0
    private var mExpectedWord115: Double = 0.0

    @Before
    fun setUp() {
        mergeContentHelper = MergeContentHelper()
        mergeContentHelper.mExpectedWord = 20
        mExpectedWord85 = mergeContentHelper.mExpectedWord * 0.85       // 17个字
        mExpectedWord115 = mergeContentHelper.mExpectedWord * 1.15      // 23个字
    }

    /**
     * 第一个数据进来
     */
    @Test
    fun testFunGroupContent_InitialData() {
        val item1 = ConvertContentItem(
            textContent = "Hello",
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )

        mergeContentHelper.groupContent(item1)
        assertEquals(1, mergeContentHelper.mGroupItemList.size)
        assertEquals(1, mergeContentHelper.mGroupItemList[0].size)
        assertEquals("Hello", mergeContentHelper.mGroupItemList[0][0].textContent)
    }

    /**
     * 第一个数据更新,由中间态过渡到完成态
     */
    @Test
    fun testFunGroupContent_SameObjUpdate() {
        // 测试同一数据的中间结果更新
        val item1 = ConvertContentItem(
            textContent = "Hello",
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )

        // 第一次添加
        mergeContentHelper.groupContent(item1)

        // 更新内容
        item1.apply {
            textContent = "Hello world"
            textType = ASR_RESULT_TYPE_INTERMEDIATE
            roleId = 1
        }
        mergeContentHelper.groupContent(item1)

        assertEquals(1, mergeContentHelper.mGroupItemList.size)
        assertEquals(1, mergeContentHelper.mGroupItemList[0].size)
        assertEquals("Hello world", mergeContentHelper.mGroupItemList[0][0].textContent)
        assertEquals(ASR_RESULT_TYPE_INTERMEDIATE, mergeContentHelper.mGroupItemList[0][0].textType)

        // 更新为最终结果
        item1.apply {
            textContent = "Hello world!"
            textType = ASR_RESULT_TYPE_VAD_FINAL
            roleId = 1
        }
        mergeContentHelper.groupContent(item1)

        assertEquals(1, mergeContentHelper.mGroupItemList.size)
        assertEquals(1, mergeContentHelper.mGroupItemList[0].size)
        assertEquals("Hello world!", mergeContentHelper.mGroupItemList[0][0].textContent)
        assertEquals("Hello world!", mergeContentHelper.mGroupItemList[0][0].textContent)
        assertEquals(ASR_RESULT_TYPE_VAD_FINAL, mergeContentHelper.mGroupItemList[0][0].textType)
    }

    /**
     * 当前组中第一个数据已经处理完毕，第二个数据来临
     * 场景1：当前组中的第一个数据已经大于等于预期字数的85%，则新来的数据单独一组
     */
    @Test
    fun testFunGroupContent_tow_come_1() {
        // 第一个数据
        val item1 = ConvertContentItem(
            textContent = genTextContent(mExpectedWord85.toInt()),
            textType = ASR_RESULT_TYPE_VAD_FINAL,
            roleId = 1
        )
        mergeContentHelper.groupContent(item1)
        assertEquals(true, item1.textContent.length >= mExpectedWord85)

        // 第二个数据
        val item2 = ConvertContentItem(
            textContent = genTextContent(10),
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )
        mergeContentHelper.mGroupItemList.add(mutableListOf(item2))
        assertEquals(2, mergeContentHelper.mGroupItemList.size)
        assertEquals(1, mergeContentHelper.mGroupItemList[0].size)
        assertEquals(1, mergeContentHelper.mGroupItemList[1].size)
    }

    /**
     * 当前组中第一个数据已经处理完毕，第二个数据来临
     * 场景2：当前组中的第一个数据小于预期字数的85%，则将新数据合并到改组
     */
    @Test
    fun testFunGroupContent_tow_come_2() {
        // 第一个数据
        val item1 = ConvertContentItem(
            textContent = genTextContent(10),
            textType = ASR_RESULT_TYPE_VAD_FINAL,
            roleId = 1
        )
        mergeContentHelper.groupContent(item1)
        assertEquals(true, item1.textContent.length < mExpectedWord85)

        // 第二个数据
        val item2 = ConvertContentItem(
            textContent = genTextContent(15),
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )
        mergeContentHelper.groupContent(item2)
        assertEquals(1, mergeContentHelper.mGroupItemList.size)
        assertEquals(2, mergeContentHelper.mGroupItemList[0].size)
    }

    /**
     * 第一个分组中有两个数据，且二个数据处于中间态
     * 当该组的的总字数超过预期字数的115%，则将改组中的第二个数据移动到下一组中
     */
    @Test
    fun testFunGroupContent_intermediate_Split() {
        // 第一个数据
        val item1 = ConvertContentItem(
            textContent = genTextContent(10),
            textType = ASR_RESULT_TYPE_VAD_FINAL,
            roleId = 1
        )
        mergeContentHelper.groupContent(item1)

        // 第二个数据
        val item2 = ConvertContentItem(
            textContent = genTextContent(2),
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )
        mergeContentHelper.groupContent(item2)

        // 第二个数据更新
        item2.apply {
            textContent = genTextContent(13)
            textType = ASR_RESULT_TYPE_INTERMEDIATE
            roleId = 1
        }
        mergeContentHelper.groupContent(item2)

        assertEquals(true, item1.textContent.length + item2.textContent.length >= mExpectedWord115)
        assertEquals(2, mergeContentHelper.mGroupItemList.size)
        assertEquals(1, mergeContentHelper.mGroupItemList[0].size)
        assertEquals(1, mergeContentHelper.mGroupItemList[1].size)
    }


    /**
     * 第一个分组中有两个数据，且二个数据处于最终态
     * 当该组的第二个数据和第一个数据不是一个讲话人，则将第二个数据移动到下一组中
     */
    @Test
    fun testFunGroupContent_final_Split_1() {
        // 第一个数据
        val item1 = ConvertContentItem(
            textContent = genTextContent(5),
            textType = ASR_RESULT_TYPE_VAD_FINAL,
            roleId = 1
        )
        mergeContentHelper.groupContent(item1)

        // 第二个数据
        val item2 = ConvertContentItem(
            textContent = genTextContent(2),
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )
        mergeContentHelper.groupContent(item2)

        // 第二个数据更新
        item2.apply {
            textContent = genTextContent(3)
            textType = ASR_RESULT_TYPE_VAD_FINAL
            roleId = 2
        }
        mergeContentHelper.groupContent(item2)

        assertEquals(2, mergeContentHelper.mGroupItemList.size)
        assertEquals(1, mergeContentHelper.mGroupItemList[0].size)
        assertEquals(1, mergeContentHelper.mGroupItemList[1].size)
    }

    /**
     * 当一个分组中有三个数据的时候，且第三个数据已经处于最终态。
     * 1. 三个数据都是一个讲话人，其当第三个数据从中间态变成完成态的时候，并没有触发差分条件
     * 2. 当第四个数据来临时， 当前组的总字数已经大于等于预期字数的85%，则将第四个数据放到下一个组
     */
    @Test
    fun testFunGroupContent_final_Split_2() {
        // 第一个数据
        val item1 = ConvertContentItem(
            textContent = genTextContent(2),
            textType = ASR_RESULT_TYPE_VAD_FINAL,
            roleId = 1
        )
        mergeContentHelper.groupContent(item1)

        // 第二个数据
        val item2 = ConvertContentItem(
            textContent = genTextContent(3),
            textType = ASR_RESULT_TYPE_VAD_FINAL,
            roleId = 1
        )
        mergeContentHelper.groupContent(item2)

        // 第三个数据
        val item3 = ConvertContentItem(
            textContent = genTextContent(1),
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )
        mergeContentHelper.groupContent(item3)

        // 第三个数据的更新
        item3.apply {
            textContent = genTextContent(13)
            textType = ASR_RESULT_TYPE_VAD_FINAL
            roleId = 1
        }
        mergeContentHelper.groupContent(item3)

        assertEquals(1, mergeContentHelper.mGroupItemList.size)
        assertEquals(3, mergeContentHelper.mGroupItemList[0].size)

        // 第四个数据
        val item4 = ConvertContentItem(
            textContent = genTextContent(5),
            textType = ASR_RESULT_TYPE_INTERMEDIATE,
            roleId = 1
        )
        mergeContentHelper.groupContent(item4)

        assertEquals(2, mergeContentHelper.mGroupItemList.size)
        assertEquals(3, mergeContentHelper.mGroupItemList[0].size)
        assertEquals(1, mergeContentHelper.mGroupItemList[1].size)
    }

    /**
     * 生成指定字数的文本
     */
    private fun genTextContent(num: Int): String {
        val characters = ('a'..'z') + ('A'..'Z') + ('0'..'9') // 包含大小写字母和数字
        val sb = StringBuilder()

        repeat(num) {
            val randomIndex = Random.nextInt(characters.size)
            sb.append(characters[randomIndex])
        }

        return sb.toString()
    }
}