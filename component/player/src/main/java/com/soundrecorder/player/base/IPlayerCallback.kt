/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/7/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player.base

interface IPlayerCallback {

    fun onActionComplete(action: String)

    fun onPlayError(extra: Int)

    fun setDuration(duration: Long)

    fun setPlayerStatus(playStatus: Int)

    fun getAudioStreamType(): Int
}