/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordAmplitudeModel
 Description:
 Version: 1.0
 Date: 2022/8/9
 Author: ********(v-zhengtai<PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/9 1.0 create
 */
package com.soundrecorder.recorderservice.controller.model;

import com.soundrecorder.base.BaseApplication;
import java.util.List;
import com.soundrecorder.wavemark.wave.WaveViewUtil;
import com.soundrecorder.wavemark.wave.load.WaveSafeLinkedList;

public class RecordAmplitudeModel {

    private final List<Integer> mAmplitudeList = new WaveSafeLinkedList();
    private int mLatestAmplitude;
    private long mCurrentTimeMillis;
    private WaveViewUtil.WaveType mWaveType = WaveViewUtil.WaveType.LARGE;

    public synchronized List<Integer> getAmplitudeList() {
        return mAmplitudeList;
    }

    public synchronized int getLatestAmplitude() {
        return mLatestAmplitude;
    }

    public synchronized void setLatestAmplitude(int latestAmplitude) {
        this.mLatestAmplitude = latestAmplitude;
    }

    public synchronized int getAmplitudeListSize() {
        return mAmplitudeList.size();
    }

    public synchronized void addOneAmplitudeCache(int amplitude) {
        this.mLatestAmplitude = amplitude;
        this.mAmplitudeList.add(amplitude);
    }

    public float getOneWaveLineTime() {
        return WaveViewUtil.getOneWaveLineTimeByWaveType(BaseApplication.getAppContext(), mWaveType);
    }

    public float getMsPerPx() {
        return WaveViewUtil.DURATION_INTERVAL / WaveViewUtil.getTimeLineGapByWaveType(BaseApplication.getAppContext(), mWaveType);
    }

    public void setWaveType(WaveViewUtil.WaveType waveType) {
        if (mWaveType != waveType) {
            mWaveType = waveType;
        }
    }

    public long getCurrentTimeMillis() {
        return mCurrentTimeMillis;
    }

    public void setCurrentTimeMillis(long currentTimeMillis) {
        this.mCurrentTimeMillis = currentTimeMillis;
    }
}
