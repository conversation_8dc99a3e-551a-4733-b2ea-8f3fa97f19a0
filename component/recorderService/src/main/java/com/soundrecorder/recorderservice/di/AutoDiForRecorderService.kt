/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForRecorderService.kt
 * * Description : AutoDiForRecorderService
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.recorderservice.di

import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import org.koin.dsl.module

object AutoDiForRecorderService {
    val recorderServiceModule = module {
        single<RecorderServiceInterface>(createdAtStart = true) {
            RecorderViewModelApi
        }
    }
}