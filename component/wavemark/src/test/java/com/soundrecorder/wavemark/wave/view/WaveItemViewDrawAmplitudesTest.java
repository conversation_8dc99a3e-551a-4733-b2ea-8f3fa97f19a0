package com.soundrecorder.wavemark.wave.view;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyFloat;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.never;
import static org.mockito.internal.verification.VerificationModeFactory.times;
import static org.powermock.api.mockito.PowerMockito.verifyPrivate;

import android.content.Context;
import android.graphics.Canvas;
import android.os.Build;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.wavemark.wave.WaveViewUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({WaveItemView.class, WaveViewUtil.class, DebugUtil.class})
@Config(sdk = Build.VERSION_CODES.S)
public class WaveItemViewDrawAmplitudesTest {

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(WaveViewUtil.class);
        PowerMockito.mockStatic(DebugUtil.class);
    }


    @After
    public void tearDown() {
    }

    @Test
    public void verify_runtimes_when_drawFirstItemAmplitude() throws Exception {
        Canvas canvas = new Canvas();
        WaveItemView waveItemView = PowerMockito.mock(WaveItemView.class);
        Whitebox.setInternalState(waveItemView, "mAmplitudeWidth", 4);
        Whitebox.setInternalState(waveItemView, "mVirtualAmpGap", 4);
        waveItemView.updateScreenWidth(1080);

        PowerMockito.doReturn(540).when(waveItemView).getWidth();
        //执行private方法
        Method method = PowerMockito.method(WaveItemView.class, "drawFirstItemAmplitude", Canvas.class);
        method.invoke(waveItemView, canvas);
        //验证private方法运行了几次
        verifyPrivate(waveItemView, times(1)).invoke("drawFirstItemAmplitude", canvas);
    }

    @Test
    public void verify_runtimes_when_drawPlayAmplitude() throws Exception {
        WaveItemView waveItemView = initWaveItemView(true);
        Canvas canvas = Mockito.mock(Canvas.class);
        //绘制波形方法
        Method drawPlayAmplitude = PowerMockito.method(WaveItemView.class, "drawPlayAmplitude", Canvas.class);

        //======================模拟波形数据从标记数据中获取的情况=================//
        //1.没有波形数据的时候,期望不会绘制
        Whitebox.setInternalState(waveItemView, "mAmplitudes", new ArrayList<Integer>());
        drawPlayAmplitude.invoke(waveItemView, canvas);
        //验证方法执行的次数
        verifyPrivate(waveItemView, times(1)).invoke("calculateAmpStartIndex");
        PowerMockito.verifyPrivate(waveItemView, times(1)).invoke("calculateAmpStartIndex");
        PowerMockito.verifyPrivate(waveItemView, times(1)).invoke("calculateAmpStartX", Mockito.anyLong());
        //0 - 540,mVirtualAmpGap = 1，所以需要绘制541次
        PowerMockito.verifyPrivate(waveItemView, times(0)).invoke("getWaveLineHeight", Mockito.anyInt(), Mockito.anyInt());

        //2.有从标记数据解析出来的数据,viewWidth=540,mVirtualAmpGap=1,0 - 540所以需要绘制541次
        waveItemView = initWaveItemView(true);
        Assert.assertEquals(600, ((List<Integer>) Whitebox.getInternalState(waveItemView, "mAmplitudes")).size());
//        PowerMockito.when(waveItemView, "getWaveLineHeight", Mockito.anyInt(), Mockito.anyInt()).thenReturn(50F);
        //执行drawPlayAmplitude
        drawPlayAmplitude.invoke(waveItemView, canvas);
        //验证方法执行的次数
        PowerMockito.verifyPrivate(waveItemView, times(1)).invoke("calculateAmpStartIndex");
        PowerMockito.verifyPrivate(waveItemView, times(1)).invoke("calculateAmpStartX", Mockito.anyLong());
        //0 - 540,mVirtualAmpGap = 1，所以需要绘制541次
        PowerMockito.verifyPrivate(waveItemView, times(541)).invoke("getWaveLineHeight", Mockito.anyInt(), Mockito.anyInt());

        //3.模拟当前itemIndex超过了数据list的大小，则绘制虚线
        //重新初始化waveItemView，不然下面的方法次数验证会算上上面的次数
        waveItemView = initWaveItemView(true);
        PowerMockito.when(waveItemView, "calculateAmpStartIndex").thenReturn(2705);
        PowerMockito.when(waveItemView, "calculateAmpStartX", anyLong()).thenReturn(0F);
        //当前item的index超过了波形数据的总数，期望画虚线
        waveItemView.setCurViewIndex(5);
        Assert.assertEquals(540, waveItemView.getWidth());
        Assert.assertEquals(0F, PowerMockito.method(WaveItemView.class, "calculateAmpStartX", long.class).invoke(waveItemView, anyLong()));
        Assert.assertEquals(2705, PowerMockito.method(WaveItemView.class, "calculateAmpStartIndex").invoke(waveItemView));
        Assert.assertEquals(600, ((List<Integer>) Whitebox.getInternalState(waveItemView, "mAmplitudes")).size());
        drawPlayAmplitude.invoke(waveItemView, canvas);
        PowerMockito.verifyPrivate(waveItemView, never()).invoke("getWaveLineHeight", anyInt(), anyInt());
        PowerMockito.verifyPrivate(waveItemView, times(541)).invoke("drawDottedLine", any(Canvas.class), anyBoolean(), anyFloat(), anyInt());

        //======================模拟波形数据从文件解析的情况=================//、
        waveItemView = initWaveItemView(false);
        //1.模拟没有波形数据则不会绘制任何波形和虚线
        Whitebox.setInternalState(waveItemView, "mDecodedAmplitudeList", new ArrayList<Integer>());
        drawPlayAmplitude.invoke(waveItemView, canvas);
        PowerMockito.verifyPrivate(waveItemView, never()).invoke("getWaveLineHeight", anyInt(), anyInt());
        PowerMockito.verifyPrivate(waveItemView, never()).invoke("drawDottedLine", any(Canvas.class), anyBoolean(), anyFloat(), anyInt());

        //2.模拟当前item是第1个item，且有从文件解析出来的数据list
        waveItemView = initWaveItemView(false);
        drawPlayAmplitude.invoke(waveItemView, canvas);
        PowerMockito.verifyPrivate(waveItemView, times(541)).invoke("getWaveLineHeight", anyInt(), anyInt());
        PowerMockito.verifyPrivate(waveItemView, never()).invoke("drawDottedLine", any(Canvas.class), anyBoolean(), anyFloat(), anyInt());

        //模拟item是第5个item，则会绘制虚线
        waveItemView = initWaveItemView(false);
        PowerMockito.when(waveItemView, "calculateAmpStartIndex").thenReturn(2705);
        PowerMockito.when(waveItemView, "calculateAmpStartX", anyLong()).thenReturn(0F);
        waveItemView.setCurViewIndex(5);
        Assert.assertEquals(540, waveItemView.getWidth());
        Assert.assertEquals(0F, PowerMockito.method(WaveItemView.class, "calculateAmpStartX", long.class).invoke(waveItemView, anyLong()));
        Assert.assertEquals(2705, PowerMockito.method(WaveItemView.class, "calculateAmpStartIndex").invoke(waveItemView));
        Assert.assertEquals(600, ((List<String>) Whitebox.getInternalState(waveItemView, "mDecodedAmplitudeList")).size());
        drawPlayAmplitude.invoke(waveItemView, canvas);
        PowerMockito.verifyPrivate(waveItemView, never()).invoke("getWaveLineHeight", anyInt(), anyInt());
        PowerMockito.verifyPrivate(waveItemView, times(541)).invoke("drawDottedLine", any(Canvas.class), anyBoolean(), anyFloat(), anyInt());
    }

    @Ignore
    private WaveItemView initWaveItemView(boolean fromMark) throws InvocationTargetException, IllegalAccessException {
        Context context = Mockito.mock(Context.class);
        WaveItemView waveItemView = PowerMockito.mock(WaveItemView.class);
        waveItemView = PowerMockito.spy(waveItemView);
        Whitebox.setInternalState(waveItemView, "mAmplitudeWidth", 1);
        Whitebox.setInternalState(waveItemView, "mVirtualAmpGap", 1);
        waveItemView.updateScreenWidth(1080);
        Assert.assertEquals(0, waveItemView.getWidth());
        Assert.assertEquals(540F, (float) Whitebox.getInternalState(waveItemView, "mCenterLineX"), 0F);
        PowerMockito.doReturn(540).when(waveItemView).getWidth();
        Assert.assertEquals(540, waveItemView.getWidth());
        waveItemView.setCurViewIndex(1);

        Method isReverseLayout = PowerMockito.method(WaveItemView.class, "isReverseLayout");
//        LinearLayout linearLayout = new LinearLayout(mContext);
        PowerMockito.doReturn(null).when(waveItemView).getParent();
        PowerMockito.doReturn(context).when(waveItemView).getContext();
        //不能用下面的方式，PowerMockito有bug，下面的方法会报错
        //PowerMockito.when(WaveViewUtil.getTimeLineGap(context)).thenReturn(1F);
        //需要用这个mockStatic方法，在startUp里面加上PowerMockito.mockStatic(WaveViewUtil.class);
        PowerMockito.when(WaveViewUtil.getLargeWaveTimeLineGap(context)).thenAnswer((Answer<Float>) invocation -> 1F);
        PowerMockito.when(WaveViewUtil.getOneLargeWaveLineTime(any(Context.class))).thenAnswer((Answer<Float>) invocation -> 1F);
        Assert.assertEquals(1F, WaveViewUtil.getOneLargeWaveLineTime(context), 0F);

        //设置rtl
        boolean isRtl = (boolean) isReverseLayout.invoke(waveItemView);
        Assert.assertFalse(isRtl);
        //        PowerMockito.when(waveItemView, "isReverseLayout").thenReturn(true);
//        isRtl = (boolean) isReverseLayout.invoke(waveItemView);
//        Assert.assertTrue(isRtl);
        Assert.assertEquals(null, waveItemView.getParent());
        //准备波形数据

        if (fromMark) {
            List<Integer> playAmplitudes = new ArrayList<>();
            for (int i = 0; i < 600; i++) {
                playAmplitudes.add(i);
            }
            Whitebox.setInternalState(waveItemView, "mAmplitudes", playAmplitudes);
        } else {
            List<Integer> playAmplitudes = new ArrayList<>();
            for (int i = 0; i < 600; i++) {
                playAmplitudes.add(i);
            }
            Whitebox.setInternalState(waveItemView, "mDecodedAmplitudeList", playAmplitudes);
        }
        return waveItemView;
    }
}


