/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  FileWrapperTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.id3tool

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config
import java.io.File
import kotlin.jvm.Throws

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class FileWrapperTest {

    @Test
    @Throws
    fun should_equals_when_init() {
        kotlin.runCatching {
            var path: String? = null
            var fileWrapper = FileWrapper(path)
            Assert.assertNull(null, fileWrapper.path)

            path = "sdcard/music/123.mp3"
            val file = Mockito.mock(File::class.java)
            Mockito.`when` { file.exists() }.thenReturn { true }
            fileWrapper = FileWrapper(file)
            Assert.assertNull(null, fileWrapper.filename)
        }
    }
}