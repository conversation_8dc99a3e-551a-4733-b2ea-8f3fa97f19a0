/******************************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - WaveViewUtil.java
 * Description:
 * Version: 1.0
 * Date : 2018-11-14
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    PengFei.Ma      2018-11-14          1.0         build this module
 ********************************************************************/
package com.soundrecorder.wavemark.wave;

import android.content.Context;

import java.util.HashMap;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

import com.soundrecorder.wavemark.R;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.base.utils.DebugUtil;

public class WaveViewUtil {

    private static final String TAG = "WaveViewUtil";

    public static final long DURATION_INTERVAL = 500L;
    /**
     * Time scale count for each WaveView
     */
    public static final int TIME_SCALE_COUNT = 12;
    /**
     * Duration of each WaveView(ms)
     */
    public static final long ONE_WAVE_VIEW_DURATION = DURATION_INTERVAL * TIME_SCALE_COUNT;
    public static final int ONE_WAVE_VIEW_DURATION_SECOND = 6;
    public static final int DEFAUL_CENTER_TIME = 5000; //5s
    public static final float AMPLITUDE_SCALE = 1F;
    public static final int MAX_AMPLITUDE = 32768;
    public static final int NUM_TWO = 2;
    public static final int NUM_3 = 3;
    public static final int NUM_4 = 4;
    public static final int NUM_8 = 8;
    public static final int NUM_TEN = 10;
    public static final int NUM_255 = 255;
    public static final float PERCENT_0 = 0F;
    public static final float PERCENT_8 = 0.08F;
    public static final float PERCENT_10 = 0.1F;
    public static final float PERCENT_15 = 0.15F;
    public static final float PERCENT_20 = 0.2F;
    public static final float PERCENT_30 = 0.3F;
    public static final float PERCENT_70 = 0.7F;
    public static final float PERCENT_90 = 0.9F;
    public static final float PERCENT_100 = 1F;
    public static final float PERCENT_110 = 1.1F;
    public static final float FLOAT_0 = 0.0F;
    public static final float FLOAT_1 = 1.0F;
    public static final float NUM_TWO_F = 2f;

    private static final int MINUTES_60 = 60;
    private static final int HOUR_10 = 10;
    private static final int SIX_MINUTES_3600 = 3600;

    /**
     * 新版本：按波形图类型缓存的相关初始化参数
     */
    private static HashMap<WaveType, Float> sTimeLineGapHashMap = new HashMap<>();
    private static HashMap<WaveType, Float> sAmplitueLineGapHashMap = new HashMap<>();
    private static HashMap<WaveType, Float> sOneWaveItemWidthHashMap = new HashMap<>();
    private static HashMap<WaveType, Float> sOneWaveItemAmplitueLineCountHashMap = new HashMap<>();
    private static HashMap<WaveType, Float> sOneWaveLineTimeHashMap = new HashMap<>();

    /**
     * 老版本：大波形图相关初始化参数
     */
    private static int sLargeWaveTimeLineGap = 0;
    private static float sLargeWaveAmplitueLineGap = 0;
    private static float sOneLargeWaveItemWidth = 0;
    private static float sOneLargeWaveItemAmplitueLineCount = 0;
    private static float sOneLargeWaveLineTime = 0;


    //波形图尺寸类型
    public enum WaveType {
        //大尺寸
        LARGE,
        //中尺寸
        MIDDLE,
        //小尺寸
        SMALL
    }

    //波形图缩放类型
    public enum ZoomType {
        //整体缩放
        ZOOM_IN,
        //局部细节（放到）
        ZOOM_OUT
    }

    //波形图拖动类型
    public enum DragType {
        //可拖动
        DRAG_ENABLE,
        //不可拖动
        DRAG_UN_ENABLE
    }

    public static float getLargeWaveTimeLineGap(Context context) {
        if (sLargeWaveTimeLineGap == 0) {
            sLargeWaveTimeLineGap = context.getResources().getDimensionPixelSize(R.dimen.wave_time_line_gap_dp36);
            DebugUtil.i(TAG, "getTimeLineGap: " + sLargeWaveTimeLineGap);
        }
        return sLargeWaveTimeLineGap;
    }

    public static float getLargeWaveAmplitueLineGap(Context context) {
        if (sLargeWaveAmplitueLineGap == 0) {
            float px500s = getLargeWaveTimeLineGap(context);
            float ampGap = px500s * Constants.WAVE_SAMPLE_INTERVAL_TIME / DURATION_INTERVAL;
            DebugUtil.i(TAG, "getAmpGap: ampGap " + ampGap + ", getTimeLineGap: " + px500s);
            sLargeWaveAmplitueLineGap = ampGap;
        }
        return sLargeWaveAmplitueLineGap;
    }

    public static float getOneLargeWaveItemWidth(Context context) {
        if (sOneLargeWaveItemWidth == 0) {
            sOneLargeWaveItemWidth = getLargeWaveTimeLineGap(context) * WaveViewUtil.TIME_SCALE_COUNT;
        }
        return sOneLargeWaveItemWidth;
    }

    public static float getOneLargeWaveItemAmplitueLineCount(Context context) {
        if (sOneLargeWaveItemAmplitueLineCount == 0) {
            sOneLargeWaveItemAmplitueLineCount = getOneLargeWaveItemWidth(context) / getLargeWaveAmplitueLineGap(context);
        }
        return sOneLargeWaveItemAmplitueLineCount;
    }

    public static float getOneLargeWaveLineTime(Context context) {
        if (Float.compare(sOneLargeWaveLineTime, 0) <= 0) {
            sOneLargeWaveLineTime = (float) ONE_WAVE_VIEW_DURATION / getOneLargeWaveItemAmplitueLineCount(context);
        }
        return sOneLargeWaveLineTime;
    }

    //------------------------------------------ 按波形图尺寸类型，获取相关初始化参数-------------------------------------------------------------
    public static float getTimeLineGapByWaveType(Context context, WaveType waveType) {
        Float timeLineGap = sTimeLineGapHashMap.get(waveType);
        if (timeLineGap == null || timeLineGap == 0) {
            if (Objects.requireNonNull(waveType) == WaveType.LARGE) {
                timeLineGap = (float) context.getResources().getDimensionPixelSize(R.dimen.wave_time_line_gap_dp36);
            } else {
                timeLineGap = (float) context.getResources().getDimensionPixelSize(R.dimen.wave_time_line_gap_dp22_5);
            }
            sTimeLineGapHashMap.put(waveType, timeLineGap);
            DebugUtil.i(TAG, "getTimeLineGapByWaveType: " + waveType.name() + ", timeLineGap: " + timeLineGap);
        }
        return timeLineGap;
    }

    public static float getAmplitueLineGapByWaveType(Context context, WaveType waveType) {
        Float amplitueLineGap = sAmplitueLineGapHashMap.get(waveType);
        if (amplitueLineGap == null || amplitueLineGap == 0) {
            float px500s = getTimeLineGapByWaveType(context, waveType);
            amplitueLineGap = px500s * Constants.WAVE_SAMPLE_INTERVAL_TIME / DURATION_INTERVAL;
            DebugUtil.i(TAG, "getAmplitueLineGapByWaveType: " + waveType.name() + ",ampGap: " + amplitueLineGap + ", px500s: " + px500s);
            sAmplitueLineGapHashMap.put(waveType, amplitueLineGap);
        }
        return amplitueLineGap;
    }

    public static float getOneWaveItemWidthByWaveType(Context context, WaveType waveType) {
        Float oneWaveItemWidth = sOneWaveItemWidthHashMap.get(waveType);
        if (oneWaveItemWidth == null || oneWaveItemWidth == 0) {
            oneWaveItemWidth = getTimeLineGapByWaveType(context, waveType) * WaveViewUtil.TIME_SCALE_COUNT;
            sOneWaveItemWidthHashMap.put(waveType, oneWaveItemWidth);
            DebugUtil.i(TAG, "getOneWaveItemWidthByWaveType: " + waveType.name() + ", oneWaveItemWidth: " + oneWaveItemWidth);
        }
        return oneWaveItemWidth;
    }

    public static float getOneWaveItemAmplitueLineCountByWaveType(Context context, WaveType waveType) {
        Float oneWaveItemAmplitueLineCount = sOneWaveItemAmplitueLineCountHashMap.get(waveType);
        if (oneWaveItemAmplitueLineCount == null || oneWaveItemAmplitueLineCount == 0) {
            oneWaveItemAmplitueLineCount = getOneWaveItemWidthByWaveType(context, waveType) / getAmplitueLineGapByWaveType(context, waveType);
            sOneWaveItemAmplitueLineCountHashMap.put(waveType, oneWaveItemAmplitueLineCount);
            DebugUtil.i(TAG, "getOneWaveItemAmplitueLineCountByWaveType: " + waveType.name()
                    + ", oneWaveItemAmplitueLineCount: " + oneWaveItemAmplitueLineCount);
        }
        return oneWaveItemAmplitueLineCount;
    }

    public static float getOneWaveLineTimeByWaveType(Context context, WaveType waveType) {
        Float oneWaveLineTime = sOneWaveLineTimeHashMap.get(waveType);
        if (oneWaveLineTime == null || Float.compare(oneWaveLineTime, 0) <= 0) {
            oneWaveLineTime = (float) ONE_WAVE_VIEW_DURATION / getOneWaveItemAmplitueLineCountByWaveType(context, waveType);
            sOneWaveLineTimeHashMap.put(waveType, oneWaveLineTime);
            DebugUtil.i(TAG, "getOneWaveLineTimeByWaveType: " + waveType.name() + ", oneWaveLineTime: " + oneWaveLineTime);
        }
        return oneWaveLineTime;
    }

    public static String getStringBySecond(int seconds) {
        int hour = seconds / SIX_MINUTES_3600;
        int minute = seconds / MINUTES_60 % MINUTES_60;
        int second = seconds % MINUTES_60;
        Locale locale = Locale.getDefault();
        String timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), minute, second, 0);

        if (hour > HOUR_10) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), hour, minute, second, 0);
            return timeStr.substring(0, timeStr.length() - Constants.THREE);
        }

        if (hour > 0) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_one_hour), hour, minute, second, 0);
            return timeStr.substring(0, timeStr.length() - Constants.THREE);
        }

        if (seconds >= 0) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), minute, second, 0);
            return timeStr.substring(0, timeStr.length() - Constants.THREE);
        }
        return timeStr;
    }

    public static void clearAll() {
        DebugUtil.i(TAG, "WaveViewUtil clear all");
        sLargeWaveTimeLineGap = 0;
        sLargeWaveAmplitueLineGap = 0;
        sOneLargeWaveItemWidth = 0;
        sOneLargeWaveItemAmplitueLineCount = 0;
        sOneLargeWaveLineTime = 0;

        sTimeLineGapHashMap.clear();
        sAmplitueLineGapHashMap.clear();
        sOneWaveItemWidthHashMap.clear();
        sOneWaveItemAmplitueLineCountHashMap.clear();
        sOneWaveLineTimeHashMap.clear();
    }

    /**
     * 从指定的值列表中，获取与指定参数最接近的值
     *
     * @param x      目标值
     * @param source 目标值列表
     */
    public static float getApproximateValue(float x, CopyOnWriteArrayList<Float> source) {
        if (source == null) {
            return Float.MAX_VALUE;
        }
        if (source.size() == 1) {
            return source.get(0);
        }
        float minDifference = Math.abs(source.get(0) - x);
        int minIndex = 0;
        for (int i = 1; i < source.size(); i++) {
            float temp = Math.abs(source.get(i) - x);
            if (temp < minDifference) {
                minIndex = i;
                minDifference = temp;
            }
        }
        return source.get(minIndex);
    }
}

