/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.view;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.NightModeUtil;
import com.soundrecorder.base.utils.ScreenUtil;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.wavemark.wave.WaveViewUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class WaveAdapter<T extends WaveItemView> extends RecyclerView.Adapter<RulerViewHolder<T>>
        implements IMarkTimeAdapter {

    public static final int FIRST_ITEM = 0;
    public static final int NORMAL_ITEM = 1;
    public static final int LAST_ITEM = 2;

    public static final int MAX_COUNT = 8400; // 14h
    private static final int DEFAULT_COUNT = 3;
    private static final int FIRST = 1;
    private static final String TAG = "WaveAdapter";

    private final Context mContext;
    private final IWaveItemViewDelegate<T> mDelegate;
    private final ArrayList<MarkDataBean> mMarkTimeList = new ArrayList<>();
    private int mTimeLineGap;
    private float mPxPerMs;
    private final boolean mNightModel;

    private int mWidth = 0;
    private int mTotalCount;
    private long mTotalTime = 0;
    // 是否显示时间线
    private boolean mNeedShowTimeLine = true;
    // 波形图尺寸类型
    private WaveViewUtil.WaveType mWaveType = WaveViewUtil.WaveType.LARGE;

    public WaveAdapter(Context context, IWaveItemViewDelegate<T> delegate) {
        mContext = context;
        mDelegate = delegate;
        // 解决小屏下进入播放详情波形上方时间会闪白
        if (ScreenUtil.isSmallScreen(context)) {
            mWidth = ScreenUtil.getRealScreenWidth();
        }
        DebugUtil.i(TAG, "screen getRealScreenWidth mWidth: " + mWidth);
        mTimeLineGap = (int) WaveViewUtil.getTimeLineGapByWaveType(context, mWaveType);
        mPxPerMs = (float) mTimeLineGap / WaveViewUtil.DURATION_INTERVAL;
        mNightModel = NightModeUtil.isNightMode(context);
    }

    public void updateWidth(int width) {
        DebugUtil.e(TAG, "updateWidth " + width);
        this.mWidth = width;
        notifyDataSetChanged();
    }

    public void setWaveType(WaveViewUtil.WaveType waveType) {
        if (mWaveType != waveType) {
            mWaveType = waveType;
            //波形图尺寸类型有改变时， 相关参数要重新计算一次
            mTimeLineGap = (int) WaveViewUtil.getTimeLineGapByWaveType(mContext, mWaveType);
            mPxPerMs = (float) mTimeLineGap / WaveViewUtil.DURATION_INTERVAL;
        }
    }

    public void setNeedShowTimeLine(boolean bNeedShowTimeLine) {
        mNeedShowTimeLine = bNeedShowTimeLine;
    }

    @Override
    public List<MarkDataBean> getMarkDataList() {
        return mMarkTimeList;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return FIRST_ITEM;
        } else if (position == mTotalCount - 1) {
            return LAST_ITEM;
        } else {
            return NORMAL_ITEM;
        }
    }

    @NonNull
    @Override
    public RulerViewHolder<T> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        T inflate = mDelegate.createNewItemView(mContext, parent);

        int itemWidth = 0;
        if (viewType == FIRST_ITEM) {
            itemWidth = (mWidth / WaveViewUtil.NUM_TWO);
        } else if (viewType == LAST_ITEM) {
            float lastAmplitudeWith =
                    (float) Math.ceil(((mTotalTime % WaveViewUtil.ONE_WAVE_VIEW_DURATION) * mPxPerMs));
            itemWidth = (mWidth / WaveViewUtil.NUM_TWO) + (int) lastAmplitudeWith;
        } else {
            itemWidth = mTimeLineGap * WaveViewUtil.TIME_SCALE_COUNT;
        }
        RecyclerView.LayoutParams layoutParams = new RecyclerView.LayoutParams(itemWidth,
                RecyclerView.LayoutParams.MATCH_PARENT);
        inflate.setLayoutParams(layoutParams);
        RulerViewHolder<T> rulerViewHolder = new RulerViewHolder<>(this, inflate);
        mDelegate.onItemViewCreated(parent, inflate);

        return rulerViewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull RulerViewHolder<T> holder, int position) {
        mDelegate.onBindItemView(holder.getWaveView(), position);

        int itemWidth = 0;
        if (position == 0) { //is first item
            itemWidth = (int) (mWidth / WaveViewUtil.NUM_TWO);
        } else if (position == mTotalCount - 1) { //is last item
            float lastAmplitudeWith =
                    (float) Math.ceil(((mTotalTime % WaveViewUtil.ONE_WAVE_VIEW_DURATION) * mPxPerMs));
            itemWidth = (mWidth / WaveViewUtil.NUM_TWO) + (int) lastAmplitudeWith;
            holder.getWaveView().setEndItemWidth(lastAmplitudeWith);
        } else {
            itemWidth = mTimeLineGap * WaveViewUtil.TIME_SCALE_COUNT;
        }
        if (mNightModel != holder.getWaveView().isNightMode()) {
            holder.getWaveView().setNightMode(mNightModel);
            holder.getWaveView().updatePaintColor();
        }
        holder.getWaveView().setCurViewIndex(position);
        holder.getWaveView().setTotalCount(mTotalCount);
        holder.getWaveView().setTotalTime(mTotalTime);
        holder.getWaveView().setMarkTimeList(mMarkTimeList);
        holder.getWaveView().updateScreenWidth(mWidth);
        holder.getWaveView().setNeedShowTimeLine(mNeedShowTimeLine);
        holder.getWaveView().setWaveType(mWaveType);
        holder.getWaveView().postInvalidate();
        if ((holder.getItemViewType() == LAST_ITEM) || (holder.getItemViewType() == FIRST_ITEM)) {
            holder.getWaveView().setLayoutParams(
                    new RecyclerView.LayoutParams(
                            itemWidth,
                            RecyclerView.LayoutParams.MATCH_PARENT
                    )
            );
        }
    }

    @Override
    public int getItemCount() {
        if (mWidth <= 0) {
            mTotalCount = 0;
        } else {
            mTotalCount =
                    (int) Math.ceil((double) mTotalTime / WaveViewUtil.ONE_WAVE_VIEW_DURATION) + FIRST;
            if (mTotalCount == 0) {
                mTotalCount = DEFAULT_COUNT;
            }
            if (mTotalTime % WaveViewUtil.ONE_WAVE_VIEW_DURATION == 0) {
                mTotalCount++;
                /*
                 * when mTotalTime is times of ONE_WAVE_VIEW_DURATION ，the second to last
                 * view has not enough space to move.So we need to judge the condition ,if it is true.
                 * we need add one empty WaveView ,it could let WaveRecycler has enough space to move.
                 */
            }
            mTotalCount = mDelegate.fixItemCount(mTotalCount);
        }
        return mTotalCount;
    }

    public void setTotalTime(long time) {
        mTotalTime = time;
    }

    public long getTotalTime() {
        return mTotalTime;
    }

    public void setMarkTimeList(List<MarkDataBean> markTimeList) {
        mMarkTimeList.clear();
        if ((markTimeList != null) && !markTimeList.isEmpty()) {
            for (MarkDataBean markDataBean : markTimeList) {
                if (markDataBean != null) {
                    mMarkTimeList.add(markDataBean);
                }
            }
            Collections.sort(mMarkTimeList);
        }
        notifyDataSetChanged();
    }

    public void removeBookMark(int index) {
        if (mMarkTimeList.size() > index) {
            mMarkTimeList.remove(index);
            notifyDataSetChanged();
        }
    }
}
