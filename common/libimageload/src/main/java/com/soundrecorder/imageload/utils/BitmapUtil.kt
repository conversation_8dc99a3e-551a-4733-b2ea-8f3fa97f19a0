package com.soundrecorder.imageload.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.VectorDrawable
import android.util.Pair
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat
import com.soundrecorder.base.utils.DebugUtil
import java.io.File

object BitmapUtil {

    private const val TAG = "BitmapUtil"

    @JvmStatic
    fun getBitmapDimen(context: Context, resourceId: Int): Pair<Int, Int> {
        var outWidth = -1
        var outHeight = -1
        val drawable = ContextCompat.getDrawable(context, resourceId)
            ?: return Pair(outWidth, outHeight)

        outHeight = drawable.intrinsicHeight
        outWidth = drawable.intrinsicWidth

        DebugUtil.i(TAG, "with: $outWidth, height: $outHeight")
        return Pair(outWidth, outHeight)
    }

    @JvmStatic
    fun getBitmapFromDrawable(context: Context, @DrawableRes drawableId: Int): Bitmap {
        val drawable = ContextCompat.getDrawable(context, drawableId)
            ?: throw IllegalArgumentException("drawable is null")
        return if (drawable is BitmapDrawable) {
            drawable.bitmap
        } else if (drawable is VectorDrawable || drawable is VectorDrawableCompat) {
            val bitmap = Bitmap.createBitmap(
                drawable.intrinsicWidth,
                drawable.intrinsicHeight,
                Bitmap.Config.ARGB_8888
            )
            val canvas = Canvas(bitmap)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)
            bitmap
        } else {
            throw IllegalArgumentException("unsupported drawable type")
        }
    }

    @JvmStatic
    fun getBitmapWithAndHeight(bitMapFilePath: String): ImageParseResult {
        val outWidth: Int
        val outHeight: Int
        val bitMapOption = BitmapFactory.Options()
        bitMapOption.inJustDecodeBounds = true
        BitmapFactory.decodeFile(bitMapFilePath, bitMapOption)
        outHeight = bitMapOption.outHeight
        outWidth = bitMapOption.outWidth
        DebugUtil.i(TAG, "getBitmapWithAndHeight with: $outWidth, height: $outHeight")
        return ImageParseResult(width = outWidth, height = outHeight)
    }

    @JvmStatic
    fun getBitmapWithAndHeight(pictureFile: File): ImageParseResult {
        val bitMapOption = BitmapFactory.Options()
        bitMapOption.inJustDecodeBounds = true
        val fileInputStream = pictureFile.inputStream()
        DebugUtil.i(TAG, "getBitmapWithAndHeight file $pictureFile fileExist ${pictureFile.exists()}, stream size ${fileInputStream.available()}")
        val result = ImageParseResult()
        fileInputStream.use {
            BitmapFactory.decodeStream(it, null, bitMapOption)
            result.height = bitMapOption.outHeight
            result.width = bitMapOption.outWidth
            DebugUtil.e(TAG, "getBitmapWithAndHeight with: ${result.width}, height: ${result.height}")
        }
        return result
    }
}