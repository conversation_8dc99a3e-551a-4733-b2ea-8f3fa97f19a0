/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ClassInfo.java
 * * Description: ClassInfo.java
 * * Version: 1.0
 * * Date : 2019/11/15
 * * Author: liuyulong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * liuyulong  2019/11/15      1.0    build this module
 ****************************************************************/
package com.soundrecorder.base.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;

public class ClassInfo {
    Class<?> mClass;
    private HashMap<String, Method> methods = new HashMap<>();
    private HashMap<String, Field> fields = new HashMap<>();

    public ClassInfo(Class<?> clazz, String className) {
        this.mClass = clazz;
    }

    void addCachedMethod(String key, Method method) {
        this.methods.put(key, method);
    }

    Method getCachedMethod(String key) {
        return this.methods.get(key);
    }

    public void addCachedField(String key, Field field) {
        this.fields.put(key, field);
    }

    public Field getCachedField(String key) {
        return this.fields.get(key);
    }
}

