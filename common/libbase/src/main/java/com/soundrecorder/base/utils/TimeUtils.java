/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: TimeUtils.java
 Description:
 Version: 1.0
 Date: 2009-12-15
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2009-12-15 create
 */

package com.soundrecorder.base.utils;

import android.content.Context;
import android.content.res.Resources;
import android.text.TextUtils;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.R;

import java.util.Calendar;
import java.util.Locale;

public class TimeUtils {
    public static final int SECONDS_1000 = 1000;
    public static final int TIME_ONE_SECOND = 1000;
    public static final int TIME_MS_100 = 100;
    public static final int TIME_MS_10000 = 10000;
    public static final long TIME_ONE_SECOND_LONG = 1000L;

    public static final long DELAY_2000 = 2000;

    private static final int MINUTES_60 = 60;
    private static final int HOUR_10 = 10;
    private static final int SIX_MINUTES_3600 = 3600;
    private static final int TWENTY_FOUR_24 = 24;
    private static final int THREE = 3;
    private static final String TAG = "TimeUtils";
    private static final int NUMBER_10 = 10;

    private static final int NUMBER_DAY_1 = 1;
    private static final int NUMBER_DAY_30 = 30;
    /*
     * Millisecond conversion to hour, minutes and milliseconds
     */
    public static String getFormatTimeExclusiveMill(long time) {
        return getFormatTimeExclusiveMill(time, false);
    }

    /**
     * 将时间转成 00:00:00 格式
     * @param time
     * @param leastOneSecond true：不足一秒返回一秒； false：默认逻辑
     * @return
     */
    public static String getFormatTimeExclusiveMill(long time, boolean leastOneSecond) {
        String timeStr = getFormatTimeByMillisecond(time, leastOneSecond);
        if (!TextUtils.isEmpty(timeStr)) {
            return timeStr.substring(0, timeStr.length() - THREE);
        } else {
            return null;
        }
    }

    /**
     * 将时间转成 00:00:00 格式
     * @param time
     * @param leastOneSecond true：不足一秒返回一秒； false：默认逻辑
     * @return
     */
    public static String getFormatTimeExclusiveMillLeastHour(long time, boolean leastOneSecond) {
        String timeStr = getFormatTimeByMillisecondLeastHour(time, leastOneSecond);
        if (!TextUtils.isEmpty(timeStr)) {
            return timeStr.substring(0, timeStr.length() - THREE);
        } else {
            return null;
        }
    }

    public static String getDisplayTimeInSec(int sec) {
        if (sec < 0L) {
            return "";
        }
        String result = String.format("%02d:%02d:%02d", sec / SIX_MINUTES_3600, sec / MINUTES_60 % MINUTES_60, sec % MINUTES_60);
        return result;
    }

    public static String getFormatTimeByMillisecond(long ms) {
        return getFormatTimeByMillisecond(ms, false);
    }

    /*
     * Millisecond conversion to hour, minutes and milliseconds
     * 将时间转成 00:00:00 格式
     * @param ms
     * @param leastOneSecond true:ms不足1秒会返回1s
     */
    public static String getFormatTimeByMillisecond(long ms, boolean leastOneSecond) {
        int ss = SECONDS_1000;
        int mi = ss * MINUTES_60;
        int hh = mi * MINUTES_60;
        int dd = hh * TWENTY_FOUR_24;

        long day = ms / dd;
        long hour = (ms - day * dd) / hh;
        long minute = (ms - day * dd - hour * hh) / mi;
        long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        long milliSecond = (ms - day * dd - hour * hh - minute * mi - second * ss) / HOUR_10;

        Locale locale = Locale.getDefault();

        if (day > 0) {
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_day), day, hour, minute, second, milliSecond);
        }

        if (hour >= HOUR_10) {
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), hour, minute, second, milliSecond);
        }

        if (hour > 0) {
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_one_hour), hour, minute, second, milliSecond);
        }

        if (ms > 0) {
            if (leastOneSecond && (minute == 0) && (second == 0)) {
                return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), 0, 1, 0);
            }
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), minute, second, milliSecond);
        }

        return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), 0, 0, 0);
    }

    public static String getFormatTimeByMillisecondLeastHour(long ms, boolean leastOneSecond) {
        int ss = SECONDS_1000;
        int mi = ss * MINUTES_60;
        int hh = mi * MINUTES_60;
        int dd = hh * TWENTY_FOUR_24;

        long day = ms / dd;
        long hour = (ms - day * dd) / hh;
        long minute = (ms - day * dd - hour * hh) / mi;
        long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        long milliSecond = (ms - day * dd - hour * hh - minute * mi - second * ss) / HOUR_10;

        Locale locale = Locale.getDefault();

        if (day > 0) {
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_day), day, hour, minute, second, milliSecond);
        }

        if (hour >= HOUR_10) {
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), hour, minute, second, milliSecond);
        }

        if (hour > 0) {
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), hour, minute, second, milliSecond);
        }

        if (ms > 0) {
            if (leastOneSecond && (minute == 0) && (second == 0)) {
                return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), 0, 0, 1, 0);
            }
            return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), 0, minute, second, milliSecond);
        }

        return String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), 0, 0, 0, 0);
    }

    public static int getSecTime(String timeFormat) {
        if (TextUtils.isEmpty(timeFormat)) {
            return 0;
        }

        int millSec = 0;
        String secondStr = "";
        int indexOfDecimalSeparator = timeFormat.indexOf(".");

        if (indexOfDecimalSeparator > 0) {
            String millSecStr = timeFormat.substring(indexOfDecimalSeparator + 1);
            millSec = Integer.parseInt(millSecStr);
            secondStr = timeFormat.substring(0, indexOfDecimalSeparator);
        } else {
            secondStr = timeFormat;
        }

        DebugUtil.i(TAG, "getSecTime, the millSec is " + millSec + ", the secondStr is " + secondStr + " ,timeFormat = " + timeFormat);
        String[] times = secondStr.split(":");
        int totalSize = (times != null) ? (times.length) : 0;

        if (totalSize == THREE) {
            int hour = Integer.parseInt(times[0]);
            int min = Integer.parseInt(times[1]);
            int sec = Integer.parseInt(times[2]);
            int result = (hour * SIX_MINUTES_3600 + min * MINUTES_60 + sec) * TIME_ONE_SECOND + millSec * HOUR_10;
            return result;
        } else if (totalSize == 2) {
            int min = Integer.parseInt(times[0]);
            int sec = Integer.parseInt(times[1]);
            int result = (min * MINUTES_60 + sec) * TIME_ONE_SECOND + millSec * HOUR_10;
            return result;
        } else {
            return 0;
        }
    }

    public static boolean compareTimeCrossBreakpoint(long inputTimeA, long inputTimeB) {
        int ss = SECONDS_1000;
        int mi = ss * MINUTES_60;
        int hh = mi * MINUTES_60;
        int h10 = hh * 10;
        int dd = hh * TWENTY_FOUR_24;
        long max = Math.max(inputTimeA, inputTimeB);
        long min = Math.min(inputTimeA, inputTimeB);
        boolean crossDay = (max > dd) && (min < dd);
        boolean cross10Hour = (max > h10) && (min < h10);
        boolean crossHour = (max > hh) && (min < hh);
        return crossDay || cross10Hour || crossHour;
    }

    public static String getDateString(Long currentTimeMillis) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(currentTimeMillis);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int seconde = calendar.get(Calendar.SECOND);
        int miliseconde = calendar.get(Calendar.MILLISECOND);
        String result = formateTwoNumber(year) + formateTwoNumber(month + 1) + formateTwoNumber(day)
                + formateTwoNumber(hour) + formateTwoNumber(minute) + formateTwoNumber(seconde)
                + formateTwoNumber(miliseconde);
        DebugUtil.i(TAG, "getDateString result: year: "
                + year + ", month: "
                + (month + 1) + ", day: " + day + ", hour: "
                + hour + ", minute: " + minute + " second: "
                + seconde + ", miliseconde: " + miliseconde);
        return result;
    }

    public static String formateTwoNumber(int input) {
        if (input < NUMBER_10) {
            return "0" + String.valueOf(input);
        } else {
            return String.valueOf(input);
        }
    }

    public static String getDurationHint(Context context, long durationMs) {
        return getDurationHint(context, durationMs, false);
    }

    public static String getDurationHint(Context context, long durationMs, boolean leastOneSecond) {
        if ((context == null) || (durationMs < 0L)) {
            return "";
        }

        long duration = durationMs / SECONDS_1000;
        long hd = duration / SIX_MINUTES_3600;
        long md = (duration - hd * SIX_MINUTES_3600) / MINUTES_60;
        long sd = duration - (hd * SIX_MINUTES_3600 + md * MINUTES_60);

        Resources r = context.getResources();
        if (hd > 0) {
            return r.getString(R.string.duration_hint_with_hour, hd, md, sd);
        } else if (md > 0) {
            return r.getString(R.string.duration_hint_with_min, md, sd);
        } else {
            return r.getString(R.string.duration_hint_with_sec, (leastOneSecond && sd == 0) ? 1 : sd);
        }
    }

    public static String getFormatContentDescriptionTimeByMillisecond(Context context, long ms) {
        int ss = SECONDS_1000;
        int mi = ss * MINUTES_60;
        int hh = mi * MINUTES_60;
        int dd = hh * TWENTY_FOUR_24;

        long day = ms / dd;
        long hour = (ms - day * dd) / hh;
        long minute = (ms - day * dd - hour * hh) / mi;
        long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        //long milliSecond = (ms - day * dd - hour * hh - minute * mi - second * ss) / HOUR_10;

        Locale locale = Locale.getDefault();

        if (context == null) {
            DebugUtil.i(TAG, "getformatContentDiscriptionTimeByMillisecond context is null, return");
            return "";
        }

        String formateString = "";
        if (day > 0) {
            formateString = context.getString(R.string.talkback_day_hour_min_seceod);
            return String.format(locale, formateString, day, hour, minute, second);
        }

        if (hour > 0) {
            formateString = context.getString(R.string.duration_hint_with_hour);
            return String.format(locale, formateString, hour, minute, second);
        }

        if (minute > 0) {
            formateString = context.getString(R.string.duration_hint_with_min);
            return String.format(locale, formateString, minute, second);
        }

        if (second > 0) {
            formateString = context.getString(R.string.duration_hint_with_sec);
            return String.format(locale, formateString, second);
        }

        formateString = context.getString(R.string.duration_hint_with_min);
        return String.format(locale, formateString, 0, 0);
    }

    /**
     * get play time talkback string
     *
     * @param duration time ms
     */
    public static String getContentDescriptionForTimeDuration(Long duration) {
        Context context = BaseApplication.getAppContext();
        return getFormatContentDescriptionTimeByMillisecond(context, duration);
    }

    public static long getFormatTimeByMilliForDays(long ms) {
        int ss = SECONDS_1000;
        int mi = ss * MINUTES_60;
        int hh = mi * MINUTES_60;
        int dd = hh * TWENTY_FOUR_24;

        long day = ms / dd;

        if (day >= NUMBER_DAY_30) {
            return NUMBER_DAY_30;
        }
        if (day > 0) {
            return day;
        } else {
            //小于一天时，也返回1天
            return NUMBER_DAY_1;
        }
    }

    public static int seconds2min(int s) {
        return s / MINUTES_60 + 1;
    }
}
