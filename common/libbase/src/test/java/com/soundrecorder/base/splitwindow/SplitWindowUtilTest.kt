/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SplitWindowUtilTest
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/6/26 1.0 create
 */

package com.soundrecorder.base.splitwindow

import android.app.Activity
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.os.Bundle
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class SplitWindowUtilTest {
    companion object {
        private const val EXTRA_LAST_IS_WINDOW_PAR = "last_is_windowparameter"
    }

    @Test
    fun should_when_putWindowParameterIntoSaveInstanceState() {
        val bundle = Bundle()
        val activity = Robolectric.buildActivity(Activity::class.java).get()
        SplitWindowUtil.putWindowParameterIntoSaveInstanceState(bundle, activity)
        Assert.assertTrue(bundle.containsKey(EXTRA_LAST_IS_WINDOW_PAR))
    }

    @Test
    fun should_when_getCurrentSplitWindowParameter() {
        val activity = Robolectric.buildActivity(Activity::class.java).get()
        val parameter = SplitWindowUtil.getCurrentSplitWindowParameter(activity)
        Assert.assertNotNull(parameter)
    }

    @Test
    fun should_when_spitSmallWindowLessThanForPlay280() {
        val activity = Mockito.mock(Activity::class.java)
        Mockito.`when`(activity.isInMultiWindowMode).thenReturn(false, true, true)
        var result = SplitWindowUtil.spitSmallWindowLessThanForPlay280(activity)
        Assert.assertFalse(result)

        val resource = Mockito.mock(Resources::class.java)
        val configuration = Mockito.mock(Configuration::class.java)
        Mockito.`when`(resource.configuration).thenReturn(configuration)
        Mockito.`when`(activity.resources).thenReturn(resource)

        configuration.screenHeightDp = 1
        result = SplitWindowUtil.spitSmallWindowLessThanForPlay280(activity)
        Assert.assertTrue(result)

        configuration.screenHeightDp = 300
        result = SplitWindowUtil.spitSmallWindowLessThanForPlay280(activity)
        Assert.assertFalse(result)
    }

    @Test
    fun should_when_spitSmallWindowLessThanForPlay540() {
        val activity = Mockito.mock(Activity::class.java)
        Mockito.`when`(activity.isInMultiWindowMode).thenReturn(false, true, true)
        var result = SplitWindowUtil.spitWindowHeightLessThanForPlay540(activity)
        Assert.assertFalse(result)

        val resource = Mockito.mock(Resources::class.java)
        val configuration = Mockito.mock(Configuration::class.java)
        Mockito.`when`(activity.resources).thenReturn(resource)
        Mockito.`when`(resource.configuration).thenReturn(configuration)

        configuration.screenHeightDp = 500
        result = SplitWindowUtil.spitWindowHeightLessThanForPlay540(activity)
        Assert.assertTrue(result)

        configuration.screenHeightDp = 600
        result = SplitWindowUtil.spitWindowHeightLessThanForPlay540(activity)
        Assert.assertFalse(result)
    }

    @Test
    fun should_when_isEnoughLayoutWaveView() {
        var params = ISplitWindChangeListener.SplitWindowParameter(
            1080, 1920,
            splitWindow = true,
            isUnfold = true
        )
        var result = SplitWindowUtil.isEnoughLayoutWaveView(params)
        Assert.assertFalse(result)

        params = ISplitWindChangeListener.SplitWindowParameter(
            1080, 1920,
            splitWindow = false,
            isUnfold = true
        )
        result = SplitWindowUtil.isEnoughLayoutWaveView(params)
        Assert.assertFalse(result)

        params = ISplitWindChangeListener.SplitWindowParameter(
            1080, 500,
            splitWindow = true,
            isUnfold = true
        )
        result = SplitWindowUtil.isEnoughLayoutWaveView(params)
        Assert.assertTrue(result)
    }
}