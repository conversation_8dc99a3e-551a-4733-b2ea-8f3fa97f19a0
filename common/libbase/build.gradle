apply from: "../../common_build.gradle"

android {
    dataBinding {
        enabled = true
    }

    sourceSets {
        main {
            res.srcDirs += ['res']
            res.srcDirs += ['../../res-strings']
        }
    }

    namespace "com.soundrecorder.base"
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    //原生————————————————————————————————————————————————————————————————————————————
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx
    // fragment
    implementation libs.androidx.fragment
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.lifecycle.runtime
    //window
    implementation libs.androidx.window
    // For Java-friendly APIs to register and unregister callbacks
    implementation libs.androidx.window.java
    // For testing
    implementation(libs.androidx.window.testing) {
        exclude group: 'junit'
    }
    //内部依赖————————————————————————————————————————————————————————————————————————————
    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.recyclerview
    implementation (libs.oplus.coui.responsiveui)
    implementation libs.oplus.coui.dialog
    testImplementation(libs.oplus.coui.core)
    testImplementation (libs.oplus.coui.responsiveui)
    testImplementation (libs.oplus.coui.recyclerview)

    implementation(libs.oplus.material)
    //涉及品牌化依赖，仅仅compileOnly
    compileOnly(libs.oplus.support.adapter)
    testImplementation(libs.oplus.support.adapter)
    //路径位置
    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon

    implementation libs.oplus.appfeature
    //opendId sdk 移植到base
    implementation libs.oplus.stdid.sdk

    api libs.oplus.statistic
    implementation libs.gson

    //依赖日志打捞的module
    implementation project(':common:RecorderLogBase')
    implementation project(':common:modulerouter')

    // Koin for Android
    implementation(libs.koin)
}
