package com.soundrecorder.common.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.R;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;
import org.robolectric.shadows.ShadowToast;

import java.util.ArrayList;

import static android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION;
import static android.content.Intent.FLAG_GRANT_WRITE_URI_PERMISSION;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.robolectric.Shadows.shadowOf;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class FileDealUtilsTest {

    private static final String MP3_PATH_TEXT = "emulated/0/Music/Recordings/Standard Recordings/1.mp3";
    private static final int SEND_ITEM_LIMIT = 500;
    private Context mContext;
    private Activity mActivity;
    private FileDealUtil mInstance;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mActivity = Robolectric.buildActivity(Activity.class).get();
        mInstance = FileDealUtil.INSTANCE;
    }

    @After
    public void tearDown() {
        mInstance = null;
        mActivity = null;
        mContext = null;
    }

    @Test
    public void should_returnTrue_when_delete() {
        boolean isPathExit = mInstance.delete(MP3_PATH_TEXT);
        assertTrue(isPathExit);
        isPathExit = mInstance.delete("");
        assertTrue(isPathExit);
    }

    @Test
    public void should_returnTrue_when_delete_uri() {
        Uri inputUri = Uri.parse("content://mediaStore");
        boolean res = mInstance.delete(inputUri);
        assertTrue(res);
    }

    @Test
    public void should_sendToWechat_when_sendRecordFile() {
        mInstance.sendRecordFile(mActivity, 0L, null);
        Intent nextStartedActivity = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        assertNull(nextStartedActivity);
        mInstance.sendRecordFile(mActivity, 1L, null);
        nextStartedActivity = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        assertNotNull(nextStartedActivity);
        assertEquals(Intent.ACTION_CHOOSER, nextStartedActivity.getAction());
        assertEquals(FLAG_GRANT_WRITE_URI_PERMISSION | FLAG_GRANT_READ_URI_PERMISSION, nextStartedActivity.getFlags());
    }

    @Test
    public void should_response_when_sendRecordFiles_different_inputs() {
        ArrayList<String> checked = spy(new ArrayList<>());
        checked.add("1");
        doReturn(SEND_ITEM_LIMIT + 1).when(checked).size();
        mInstance.sendRecordFiles(mActivity, checked, null);
        Intent nextStartedActivity = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        String textOfLatestToast = ShadowToast.getTextOfLatestToast();
        assertNull(nextStartedActivity);
        assertNotNull(textOfLatestToast);
        Assert.assertEquals(mActivity.getResources().getString(R.string.send_record_item_exceed_limit), textOfLatestToast);
        doCallRealMethod().when(checked).size();
        mInstance.sendRecordFiles(mActivity, checked, null);
        nextStartedActivity = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        assertNotNull(nextStartedActivity);
        assertEquals(Intent.ACTION_CHOOSER, nextStartedActivity.getAction());
    }

    @Test
    public void should_response_when_renameAgain() {
        Record record = new Record();
        record.setRelativePath("path/test");
        record.setDisplayName("displayName");
        FileDealUtil.renameAgain(null, "newDisplayName");
        FileDealUtil.renameAgain(record, "newDisplayName");
        record.setDisplayName("displayName.px");
        FileDealUtil.renameAgain(record, "newDisplayName");
    }

//    @Test
//    @Ignore
//    @Config(com.oplus.soundrecorder.shadows = {ShadowRecorderUtilBelowQ.class})
//    public void should_response_when_deleteRecord_belowQ() throws Exception {
//        MockedStatic<ConvertDeleteUtil> convertDeleteUtilMockedStatic = mockStatic(ConvertDeleteUtil.class);
//        RecorderDBUtil instance = mock(RecorderDBUtil.class);
//        MockedStatic<RecorderDBUtil> mockedStatic = mockStatic(RecorderDBUtil.class);
//        mockedStatic.when(() -> RecorderDBUtil.getInstance(any())).thenReturn(instance);
//        boolean result = mInstance.deleteRecord(mActivity, "", 2222L, RECORD_TYPE_STANTARD);
//        assertTrue(result);
//        convertDeleteUtilMockedStatic.verify(() -> ConvertDeleteUtil.deleteConvertData(any(), anyLong()), times(1));
//        verify(instance, times(1)).deleteRecordByPath(anyString(), anyBoolean());
//        result = mInstance.deleteRecord(mActivity, "path/test", 2222L, RECORD_TYPE_STANTARD);
//        assertTrue(result);
//        convertDeleteUtilMockedStatic.verify(() -> ConvertDeleteUtil.deleteConvertData(any(), anyLong()), times(2));
//        verify(instance, times(2)).deleteRecordByPath(anyString(), anyBoolean());
//        convertDeleteUtilMockedStatic.close();
//        mockedStatic.close();
//    }
//
//    @Test
//    @Config(com.oplus.soundrecorder.shadows = ShadowRecorderUtil.class)
//    public void should_call_deleteRecordByRelativePathAndDisplayName_when_deleteRecordDBBatch() {
//        RecorderDBUtil instance = mock(RecorderDBUtil.class);
//        MockedStatic<RecorderDBUtil> mockedStatic = mockStatic(RecorderDBUtil.class);
//        mockedStatic.when(() -> RecorderDBUtil.getInstance(any())).thenReturn(instance);
//        List<Record> records = new ArrayList<>();
//        Record record = new Record();
//        record.setRelativePath("path/test");
//        record.setDisplayName("displayName");
//        records.add(record);
//        mInstance.deleteRecordDBBatch(records);
//        verify(instance).deleteRecordByRelativePathAndDisplayName(anyString(), anyString(), anyBoolean());
//        mockedStatic.close();
//    }
//
//    @Test
//    @Config(com.oplus.soundrecorder.shadows = ShadowRecorderUtilBelowQ.class)
//    public void should_call_deleteRecordByPath_when_deleteRecordDBBatch_belowQ() throws Exception {
//        RecorderDBUtil instance = mock(RecorderDBUtil.class);
//        MockedStatic<RecorderDBUtil> mockedStatic = mockStatic(RecorderDBUtil.class);
//        mockedStatic.when(() -> RecorderDBUtil.getInstance(any())).thenReturn(instance);
//        List<Record> records = new ArrayList<>();
//        Record record = new Record();
//        record.setData("testData");
//        records.add(record);
//        mInstance.deleteRecordDBBatch(records);
//        verify(instance).deleteRecordByPath(anyString(), anyBoolean());
//        mockedStatic.close();
//    }

}