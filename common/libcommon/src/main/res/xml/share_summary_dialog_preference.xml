<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="374dp">
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:layout="@layout/share_summary_text_panel"
            android:selectable="false"
            app:couiShowDivider="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="false"
            android:icon="@drawable/share_summary_word"
            android:key="share_summary_word_type"
            android:persistent="false"
            app:couiIconStyle="round"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true"
            app:coui_jump_mark="@null" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="true"
            android:icon="@drawable/share_summary_txt"
            android:key="share_summary_txt_type"
            android:persistent="false"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true"
            app:coui_jump_mark="@null" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="false"
            android:icon="@drawable/share_summary_pdf"
            android:key="share_summary_pdf_type"
            android:persistent="false"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true"
            app:coui_jump_mark="@null" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.soundrecorder.common.flexible.ShareSummaryAndTextPreference
            android:key="share_summary_dialog_button"
            android:layout="@layout/share_sunmmary_button_panel" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
</PreferenceScreen>