/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  FileObserverMainHandler
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.fileobserve

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import java.lang.ref.WeakReference

class FileObserverMainHandler(looper: Loop<PERSON>, fileObserver: MultiFileObserver) : Handler(looper) {

    companion object {
        private const val MSG_ID = 10221
        private const val MSG_CONTENT_EVENT_ID = "eventId"
        private const val MSG_CONTENT_PATH = "path"
        private const val MSG_CONTENT_ALL_PATH = "allPath"
    }

    private val instance = WeakReference<MultiFileObserver>(fileObserver)

    fun dispatchMessage(eventId: Int, path: String?, allPath: String?) {
        val data = Bundle().also {
            it.putInt(MSG_CONTENT_EVENT_ID, eventId)
            it.putString(MSG_CONTENT_PATH, path)
            it.putString(MSG_CONTENT_ALL_PATH, allPath)
        }

        this.obtainMessage(MSG_ID).also {
            it.data = data
            sendMessage(it)
        }
    }

    override fun handleMessage(msg: Message) {
        if (msg.what == MSG_ID) {
            msg.data?.let {
                instance.get()?.onEvent(
                    it.getInt(MSG_CONTENT_EVENT_ID, 0),
                    it.getString(MSG_CONTENT_PATH),
                    it.getString(MSG_CONTENT_ALL_PATH)
                )
            }
        }
    }
}