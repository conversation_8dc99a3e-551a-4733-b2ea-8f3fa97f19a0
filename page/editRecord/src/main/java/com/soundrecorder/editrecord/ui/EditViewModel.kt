/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditViewModel
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.ui

import android.content.ContentUris
import android.os.AsyncTask
import android.os.Bundle
import android.os.SystemClock
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.base.PlayerHelperBasicCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.utils.AmpFileUtil
import com.soundrecorder.editrecord.ClipRecord
import com.soundrecorder.editrecord.ClipTask
import com.soundrecorder.editrecord.R
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.wavemark.model.AmpAndMarkModel
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource
import com.soundrecorder.wavemark.wave.view.WaveRecyclerView
import java.util.concurrent.ExecutionException
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import kotlin.math.ceil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull

class EditViewModel : ViewModel(), PlayerHelperBasicCallback {

    private var mNeedWaveLineCount = 0
    private val mQueue = LinkedBlockingQueue<Runnable>()
    private val mExecutor = ThreadPoolExecutor(1, 1, 180, TimeUnit.SECONDS, mQueue)

    var playerController = EditPlayerController(this).also { it.init() }
    var clipTaskViewModel = ClipTaskViewModel()
    var clipTask: ClipTask? = null

    var isClipped = MutableLiveData(false)
    var isShowSaveDialog = MutableLiveData(false)
    var isShowSavingDialog = MutableLiveData(false)
    var ampList = MutableLiveData<MutableList<Int>>(ArrayList())
    var cutStartTime = MutableLiveData<Long>()
    var cutEndTime = MutableLiveData<Long>()
    var recordType = MutableLiveData(-1)
    var markDataList = MutableLiveData(ArrayList<MarkDataBean>())
    var isPrepareAmplitudeAndMark = MutableLiveData(false)
    var oneWaveLineTime = 0f
    var editClickListener: View.OnClickListener? = null
    var amplitudeListUtil: AmplitudeListUtil? = null
    var recordId: Long = -1
    var playPath: String? = null
    var playName = MutableLiveData<String>()
    var mimeType: String? = null
    var needSyncRulerView = false
    var cacheSaveDialogName: String? = null
    var readMarkTagJob: Job? = null
    var mIsFromOtherApp = false
    var mClickCancel = false

    var arguments: Bundle? = null
        set(value) {
            if (field == null) {
                field = value
                playPath = field?.getString("playPath")
                playName.value = field?.getString("playName")
                mimeType = field?.getString("mimeType")
                recordType.value = field?.getInt("recordType", -1)
                playerController.setOCurrentTimeMillis(field?.getLong("seekto", 0) ?: 0)
                //q
                recordId = field?.getLong("recordId", -1) ?: -1
                mIsFromOtherApp = field?.getBoolean("isFromOtherApp", false) ?: false
                markDataList.value = field?.getParcelableArrayList("mark")
                playerController.setPlayUri(MediaDBUtils.genUri(recordId), false)
            }
        }

    init {
        editClickListener = View.OnClickListener { view ->
            when (view.id) {
                R.id.remove -> doClickDelete()

                R.id.extract -> doClickExtract()
            }
        }

        oneWaveLineTime = WaveViewUtil.getOneLargeWaveLineTime(BaseApplication.getAppContext())
    }

    private fun doClickDelete() {
        control(ClipRecord.OP_DELETE)
        CuttingStaticsUtil.addTrimDelete()
    }

    private fun doClickExtract() {
        control(ClipRecord.OP_EXTRACT)
        CuttingStaticsUtil.addTrimExtract()
    }


    private fun control(opType: Long) {
        if (isQuickClick) {
            return
        }
        if (playerController.getPlayUri() == null) {
            return
        }
        mClickCancel = false
        val rowId = playerController.getPlayUri()?.let(ContentUris::parseId)
        val clipStartDuration = cutStartTime.value
        val clipEndDuration = cutEndTime.value
        val totalDuration = playerController.getDuration()
        if (rowId == null || clipStartDuration == null || clipEndDuration == null) {
            DebugUtil.e(logTag, "control clipStartDuration or clipEndDuration is null.")
            return
        }
        playerController.pausePlay()
        clipTask = ClipTask(clipTaskViewModel, (recordType.value ?: 0))
        clipTask?.setObjAmplitudes((ampList.value ?: ArrayList()))
        clipTask?.setMarkList(markDataList.value ?: ArrayList())
        clipTask?.executeOnExecutor(
            AsyncTask.THREAD_POOL_EXECUTOR,
            rowId,
            clipStartDuration,
            clipEndDuration,
            totalDuration,
            opType
        )
    }

    fun readMarkTag() {
        DebugUtil.d(logTag, "readMarkTAG")
        amplitudeListUtil = AmplitudeListUtil(
            BaseApplication.getAppContext(),
            playPath,
            MediaDBUtils.genUri(recordId),
            false
        )

        readMarkTagJob = viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val duration = playerController.loadDuration()
                var model: AmpAndMarkModel? = null
                DebugUtil.i(logTag, "readAmpAndMark START, duration:$duration")
                withTimeoutOrNull(EditRecordFragment.READ_MARK_TIMEOUT.toLong()) {
                    model = readAmpAndMark()
                }
                DebugUtil.i(logTag, "readAmpAndMark READ COMPLET ")
                if ((model?.ampList != null) && (model?.ampList?.size ?: 0) > 0) {
                    prepareAmplitudeAndMark(model, duration)
                } else {
                    DebugUtil.i(logTag, "get ampList is null orEmpty ")
                    // 为了解决之前版本wav裁切，点击提取后杀死进程保存的文件，写入header为0，导致获取音频size、data 错误，导致波形不显示的问题
                    model?.ampList = arrayListOf()
                    model?.ampList?.add(0)

                    prepareAmplitudeAndMark(model)
                }
            }
            if (!TextUtils.isEmpty(playPath)) {
                playerController.prepareToPlay(playerController.getPlayUri())
            }
            DebugUtil.i(logTag, "readAmpAndMark set prepareAmplitudAndMark true")
            isPrepareAmplitudeAndMark.value = true
        }
    }

    fun getCurrentTime(): Long {
        return playerController.getCurrentPosition()
    }

    private fun readAmpAndMark(): AmpAndMarkModel {
        val model = AmpAndMarkModel()
        DebugUtil.i(logTag, "readAmpAndMark amp get start ")
        try {
            model.ampList = amplitudeListUtil?.ampList
            DebugUtil.i(logTag, "readAmpAndMark amp split end ")

            val markString = amplitudeListUtil?.markString
            if (markString != null && !TextUtils.isEmpty(markString)) {
                model.markString = markString
            }
        } catch (e: ExecutionException) {
            DebugUtil.e(logTag, "readAmpAndMark ExecutionException error", e)
        } catch (e: InterruptedException) {
            DebugUtil.e(logTag, "readAmpAndMark InterruptedException error", e)
        }
        return model
    }


    private fun prepareAmplitudeAndMark(model: AmpAndMarkModel?, defaultDuration: Long = 0) {
        if (model?.ampList != null && model.ampList.size > 0) {
            ampList.value?.clear()
            ampList.value?.addAll(model.ampList)
            DebugUtil.i(logTag, "prepareAmplitudeAndMark start correct amp")
            ampList.postValue(correctAmplitudeList(defaultDuration))
            DebugUtil.i(logTag, "prepareAmplitudeAndMark end correct apm")
            if ((ampList.value ?: ArrayList()).isNotEmpty()) {
                writeAmpData()
            }
        }
        markDataList.postValue(markDataList.value)
    }


    private fun writeAmpData() {
        if (!AmpFileUtil.ampFileIsExists(BaseApplication.getAppContext(), playPath)) {
            DebugUtil.v(logTag, "begin writeAmplitude.")
            mExecutor.execute {
                if ((ampList.value ?: ArrayList()).isNotEmpty()) {
                    AmplitudeListUtil.writeAmpData(
                        BaseApplication.getAppContext(),
                        playPath,
                        playerController.getPlayUri(),
                        ampList.value
                    )
                }
            }
        }
    }


    private fun correctAmplitudeList(defaultDuration: Long = 0): ArrayList<Int> {
        var amp = ArrayList<Int>(ampList.value ?: emptyList())
        var duration = playerController.getDuration()
        if (duration == 0L) {
            duration = defaultDuration
        }
        mNeedWaveLineCount = ceil(duration / oneWaveLineTime.toDouble()).toInt()
        DebugUtil.i(logTag, "duration=$duration mNeedWaveLineCount=$mNeedWaveLineCount")

        if (amp.isNullOrEmpty()) {
            return ArrayList()
        }
        amplitudeListUtil?.releaseSound()
        if (mNeedWaveLineCount > amp.size) {
            var lastAmplitude = 0
            var addAmplitude = 0
            val addSize = mNeedWaveLineCount - amp.size
            lastAmplitude = amp[amp.size - 1]
            for (i in 0 until addSize) {
                addAmplitude = (lastAmplitude * Math.random()).toInt()
                amp.add(addAmplitude)
            }
        } else if (mNeedWaveLineCount > 0 && mNeedWaveLineCount < amp.size) {
            val subList = amp.subList(0, mNeedWaveLineCount)
            amp = ArrayList(subList)
        }
        return amp
    }


    fun correctAmplitudeList(data: List<Int>?) {
        mNeedWaveLineCount =
            ceil(playerController.getDuration() / oneWaveLineTime.toDouble()).toInt()
        if (data == null) {
            return
        }
        if (amplitudeListUtil != null) {
            amplitudeListUtil?.releaseSound()
        }
        val len = data.size
        if (len in 1 until mNeedWaveLineCount) {
            var lastAmplitude = 0
            var addAmplitude = 0
            lastAmplitude = data[len - 1]
            for (i in 0 until mNeedWaveLineCount - len) {
                addAmplitude = (lastAmplitude * Math.random()).toInt()
                ampList.value?.add(addAmplitude)
            }
        } else if (mNeedWaveLineCount > 0 && mNeedWaveLineCount < len) {
            ampList.value = ArrayList(
                ampList.value?.subList(0, mNeedWaveLineCount)
                    ?: emptyList()
            )
        }
        ampList.postValue(ampList.value)
    }

    /**
     * 校正标记列表
     */
    fun correctMarkData(data: ArrayList<MarkDataBean>?) {
        if (data == null || data.isEmpty()) {
            return
        }
        val duration = playerController.getDuration()

        val len = data.size
        val lastTime = data[len - 1].timeInMills
        DebugUtil.d(logTag, "correctMarkData lastTime:$lastTime duration:$duration ")
        if (lastTime <= duration) { // 判断最后一个标记是否超过了音频时长
            DebugUtil.d(logTag, "correctMarkData don't need correct mark data")
            return
        }

        data.removeAll {
            it.timeInMills > duration
        }

        //更新数据
        markDataList.postValue(data)
    }

    fun cancelClipTask(needDelete: Boolean) {
        clipTask?.cancelAndDelete(needDelete)
    }

    override fun onCleared() {
        DebugUtil.i(logTag, "on cleared")
        super.onCleared()
        playerController.releasePlay()
        playerController.onRelease()
        cancelClipTask(false)
        clipTask?.release()
        clipTask = null
    }

    fun playBtnClick() {
        if (!playerController.isWholePlaying() && playerController.currentTimeMillis.getValueWithDefault() >= cutEndTime.getValueWithDefault()) {
            playerController.seekTime(cutStartTime.getValueWithDefault())
        }
        playerController.playBtnClick()
    }

    fun onActivityFinish() {
        DebugUtil.d(logTag, "onActivityFinish")
        amplitudeListUtil?.release()
        playerController.releasePlay()
        writeAmpData()

        readMarkTagJob?.cancel()
        readMarkTagJob = null
        editClickListener = null
    }


    //-------------------------------------------------listener---------------------------------------------------

    val mDragListener = WaveRecyclerView.DragListener {
        when (playerController.playerState.value) {
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> {
                DebugUtil.i(logTag, "onDraged")
                playerController.waveDragWhenPause = true
            }
        }
        CuttingStaticsUtil.addTrimDragWave()
    }

    val maxAmplitudeSource = object : MaxAmplitudeSource {
        override fun getMaxAmplitude(): Int {
            return NumberConstant.NUM_10000
        }

        override fun getTime(): Long {
            return playerController.getCurrentPosition()
        }

        override fun getRecorderState(): Int {
            return playerController.playerState.value ?: PlayStatus.PLAYER_STATE_HALTON
        }
    }

    fun onMarkClicked(mark: MarkDataBean) {
        DebugUtil.d(
            logTag,
            "onMarkClick, the secTime is ${mark.correctTime} ,markText = ${mark.markText}"
        )
        // 业务逻辑：矫正选择起点时间大于标记时间，则扩大起点至标记处
        if (cutStartTime.getValueWithDefault() > mark.correctTime) {
            cutStartTime.value = mark.correctTime
        }
        // 业务逻辑：矫正选择结束时间小于标记时间，则扩大起点至标记处
        if (cutEndTime.getValueWithDefault() < mark.correctTime) {
            cutEndTime.value = mark.correctTime
        }
        needSyncRulerView = true
        playerController.seekTime(mark.correctTime)
        BuryingPoint.seekToMarkTagWhenCutting(recordType.value)
    }

    fun onTimerRefreshTime(timeTickMillis: Long) {
        needSyncRulerView = false
        playerController.setOCurrentTimeMillis(timeTickMillis)
        playerController.lastPlayProgressTime = timeTickMillis
    }

    override fun getPlayerName(): MutableLiveData<String> {
        return playName
    }

    override fun getPlayerMimeType(): String? {
        return mimeType
    }

    companion object {
        private const val logTag = "EditViewModel"
        private var sLastClick = SystemClock.elapsedRealtime()
        private const val QUICK_CLICK_INTERVAL = 700

        val isQuickClick: Boolean
            get() {
                val thisClick = SystemClock.elapsedRealtime()
                return if (thisClick - sLastClick < QUICK_CLICK_INTERVAL) {
                    true
                } else {
                    sLastClick = thisClick
                    false
                }
            }
    }
}