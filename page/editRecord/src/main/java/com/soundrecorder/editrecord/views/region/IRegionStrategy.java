/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: IRegionStrategy
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.region;

import android.graphics.Canvas;

public interface IRegionStrategy {

    boolean isDraw();

    void drawStart(Canvas canvas, int viewIndex);

    void drawEnd(Canvas canvas, int viewIndex);

    void drawZone(Canvas canvas, int viewIndex);

    void updateViewHeight();

    /**
     * 更新波形view高度
     * @param height
     */
    void setItemViewHeight(float height);
}
