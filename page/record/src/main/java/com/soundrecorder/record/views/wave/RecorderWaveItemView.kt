/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecorderWaveItemView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.record.views.wave

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.record.R
import com.soundrecorder.record.views.wave.anima.SpringInterpolator
import com.soundrecorder.record.views.wave.anima.StartRecordPathInterpolator
import com.soundrecorder.wavemark.wave.WaveViewUtil.FLOAT_1
import com.soundrecorder.wavemark.wave.WaveViewUtil.NUM_TWO
import com.soundrecorder.wavemark.wave.view.WaveItemView
import kotlin.math.ceil

class RecorderWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) :
    WaveItemView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "RecorderWaveItemView"
        //动效的四种状态，默认为INIT
        private const val ENTER_ANIMATION_INIT = -1

        //正在执行动效
        private const val ENTER_ANIMATION_DOING = 1

        //动效自己执行完成，等待录制界面调用停止动效方法，期间会一直绘制虚线，避免闪烁
        private const val ENTER_ANIMATION_WAITING = 2

        //制界面调用停止动效方法后会变成完成状态，接下来绘制真实波形
        private const val ENTER_ANIMATION_FINISHED = 3

        //波形绘制动画持续的时间
        private const val DRAW_AMPLITUDE_DURATION = 600

        //进入动效的总时长 = 单个波形变化的时长 250ms + 波形从中心线移动到两端的时长 300ms
        //波形从中心线移动到两端的时长
        const val ENTER_ANIMATION_MOVE_DURATION = 450f

        //单个波形进行高度变化的时长
        const val ENTER_ANIMATION_CHANGE_DURATION = 400f

        //单个波形进行高度增加的时长和alpha变大的时长一致
        const val ENTER_ANIMATION_RISE_DURATION = 267f

        //单个波形进alpha变小的时长，剩下的50ms时长alpha保持恒定
        const val ENTER_ANIMATION_FALL_ALPHA_DURATION = 233f

        //进入动效波形的 40% alpha
        const val ALPHA_40_PERCENT = (255 * 0.4).toInt()

        //进入动效波形的 20% alpha
        const val ALPHA_20_PERCENT = (255 * 0.2).toInt()

        const val ALPHA_50_PERCENT = (255 * 0.5).toInt()
        const val ALPHA_100_PERCENT = (255 * 1.0).toInt()

        const val NUM_INT_2 = 2

        const val NUM_INT_16 = 16
    }

    private var enterAnimationPaint: Paint? = null
    private var mWaveEnterAnimColor = 0

    //进入动效插值器
    private var recordPathInterpolator: StartRecordPathInterpolator? = null

    //单个波形生成动效插值器
    private var springInterpolator: SpringInterpolator? = null

    //绘制进入动效的状态
    private var mDrawStartAnimationState = ENTER_ANIMATION_INIT

    //是否正在执行进入动画，默认为false。
    var mIsDrawStartAnimation = false

    //记录开始执行进入动效的时间
    var doEnterAnimationTimeMillis: Long = 0

    //进入波形动效的默认高度
    private var mEnterAnimationDefaultHeight = NUM_INT_2

    //进入波形动效的可变高度
    private var mEnterAnimationChangeHeight = NUM_INT_16

    //波形绘制动画持续的时间内移动的距离
    private var animDistance = 0f

    //录制器是否正在录制(state = RecorderService.RECORDING)
    var mIsRecording = false

    private var mPreCurrentBgX = 0f
    private var mPreIndex = 0

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun initBasicInfo(context: Context) {
        super.initBasicInfo(context)

        animDistance = mPxPerMs * DRAW_AMPLITUDE_DURATION
        mEnterAnimationDefaultHeight = context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp1)
        mEnterAnimationChangeHeight = context.resources.getDimensionPixelOffset(R.dimen.dp4)

        //录制波形插值器
        recordPathInterpolator = StartRecordPathInterpolator()
        //录制波形插值器
        springInterpolator = SpringInterpolator()
    }

    override fun initPaint(context: Context) {
        super.initPaint(context)

        //进入录制页动效的paint
        mWaveEnterAnimColor = context.getColor(com.soundrecorder.wavemark.R.color.wave_enter_animation_color)
        enterAnimationPaint = Paint().also {
            it.strokeCap = Paint.Cap.ROUND
            it.style = Paint.Style.FILL
            it.color = mWaveEnterAnimColor
            it.strokeWidth = mAmplitudeWidth
        }
    }

    override fun updatePaintColor() {
        mWaveEnterAnimColor = context.getColor(com.soundrecorder.wavemark.R.color.wave_enter_animation_color)
        enterAnimationPaint?.color = mWaveEnterAnimColor

        super.updatePaintColor()
    }

    override fun onDraw(canvas: Canvas) {
        //绘制刻度尺和时间文字
        drawRuler(canvas)
        //检查是否绘制进入动效，绘制动效则不绘制后面的其他内容
        if (checkEnterAnimation(canvas)) {
            return
        }
        //绘制录制/播放波形
        drawAmplitude(canvas)
        //绘制标记
        drawBookmark(canvas)
    }

    /**
     * 检查是否需要绘制进入动效
     * mIsDrawStartAnimation: 是否正在绘制进入动效，recordActivity会在动效时间倒计时结束后修改为false，通过adapter传递进来
     * mDrawStartAnimationState：当前动效的执行状态
     * 如果动效结束后波形还没有数据，则绘制连续虚线，等待波形数据,状态为 ENTER_ANIMATION_WAITING
     * 如果动效结束前已经有波形数据了，则动效结束后立即绘制波形
     */
    private fun checkEnterAnimation(canvas: Canvas): Boolean {
        return if (mIsDrawStartAnimation) {
            when (mDrawStartAnimationState) {
                ENTER_ANIMATION_DOING -> drawEnterAnimation(canvas)
                ENTER_ANIMATION_WAITING -> drawWaitingDottedLine(canvas)
                else -> {
                    mDrawStartAnimationState = ENTER_ANIMATION_DOING
                    drawEnterAnimation(canvas)
                }
            }
            true
        } else {
            if (mDrawStartAnimationState == ENTER_ANIMATION_WAITING) {
                drawWaitingDottedLine(canvas)
            }

            false
        }
    }

    /**
     * 在进入动画结束到绘制实际波形的期间，可能波形list为空
     * 这时候需要绘制水平虚线，等待list数据加载，不然期间出现短暂空白，看起来会闪烁一下
     */
    private fun drawWaitingDottedLine(canvas: Canvas) {
        if (amplitudes?.isEmpty() != false) {
            var currentX = 0f
            var waveStartY = 0
            var waveEndY = 0
            val location = IntArray(2)
            val viewWidth = width
            getLocationInWindow(location)
            if (location[0] > mScreenWidth) {
                return
            }
            while (currentX <= viewWidth) {
                waveStartY = getStartYByHeight(mDottedLineHeight)
                waveEndY = getEndYByHeight(mDottedLineHeight)
                canvas.drawLine(
                    currentX,
                    waveStartY.toFloat(),
                    currentX,
                    waveEndY.toFloat(),
                    mHorizontalDashPaint
                )
                currentX += mVirtualAmpGap
            }
            invalidate()
        }
    }

    /**
     * 绘制首页进入动效
     * 效果：从中心线开始向屏幕两侧展开，整体呈现水波纹效果。波形变化为先高后低，并且颜色alpha从0到40%黑色然后递减到20%黑色。
     * 方案：
     * 1.在刚进入执行动画的期间，阻止recycle人view滚动，等动效完成之后，继续滚动。
     * 2.根据当前时间更新单个波形高度，靠近中心线的波形先变化高度，远离中心线的波形后变化高度,从中心线向两侧一次执行高度变化的动画，整体开起来波形就是向两侧移动。
     * 具体实现：
     * 1.计算得到时间差 diffTime：记录一个开始执行动画的时间 startTime,当前时间 currentTime - 开始时间 startTime = 时间差 diffTime。
     * 2.计算某个波形开始进行高度变化的延迟 delayTime：
     * 计算当前位置x坐标和中心线的距离 diffX
     * delayTime = (diffX / 屏幕宽度的一半) * 进入动效持续的总时间 animationTotalDuration
     * 3.单个波形从开始变化高度到结束变化高度持续 animationDuration，在此期间根据提供的插值器 recordPathInterpolator 进行高度变化。
     * 单个波形某个时刻的高度 height 分为两种情况:
     * if (diffTime < delayTime || diffTime > delayTime + animationDuration) {
     * 说明还没到需要执行高度变化的时间 或者 已经超过了完成高度变化的时间
     * height = 0
     * } else {
     * 说明正在进度高度变化的期间内
     * 当前时刻占总变化时间的百分比 percent = (diffTime - delayTime) / animationDuration
     * height = recordPathInterpolator.getInterpolation(percent) * maxHeight
     * }
     * 4.如果是在执行进入动效的期间，则只绘制时间刻度和文字。动效完成之后，则屏蔽绘制动效，绘制波形等其他信息。
     * 5.因为上升曲线和下降曲线速度不一样，所以需要用2个插值器，还需要2个alpha插值器。所以使用自定义插值器 StartRecordPathInterpolator。
     */
    private fun drawEnterAnimation(canvas: Canvas) {
        //单个波形在所有波形数据list中的index
        val amplitueIndex: Int = calculateAmpStartIndex()
        //单个波形在itemVIew中的位置，从左向右计算，不区分RTL
        var currentX: Float = calculateAmpStartX(amplitueIndex.toLong())
        if (currentX > mScreenWidth) {
            //如果起始位置就已经超过了一个屏幕宽度，则不需要执行进入动效
            return
        }

        //用于获取当前item在屏幕中的位置，在绘制波形的时候用于判断绘制的距离和高度
        val location = IntArray(2)
        getLocationInWindow(location)

        //单个波形的起始Y值
        var waveStartY: Int
        //单个波形的结束Y值
        var waveEndY: Int
        //默认为从2开始进行高度变化知道达到最高值再递减
        var waveHeight = 0F
        //实际应该显示的alpha值（0-255）
        var alphaInt = 0
        //依次绘制单根波形
        while (currentX <= width) {
            if (location[0] + currentX > mScreenWidth) {
                //如果绘制到某个波形的x位置已经超过了屏幕的宽度则本次绘制完成
                break
            }
            val currentAnimationTime = calculateAnimationTime(width, location[0], currentX)
            when {
                currentAnimationTime < 0 -> {
                    //当前位置的单根波形还没到开始执行动画的时间，则不绘制,进行下一次循环（要求不绘制虚线）
                    currentX += mVirtualAmpGap
                    continue
                }
                currentAnimationTime <= ENTER_ANIMATION_CHANGE_DURATION -> {
                    //在执行动画期间，按照给定的贝塞尔曲线进行高度变化和透明度变化
                    recordPathInterpolator?.let {
                        waveHeight = it.getHeightInterpolation(currentAnimationTime,
                            mEnterAnimationDefaultHeight, mEnterAnimationChangeHeight)
                        alphaInt = it.getAlphaInterpolation(currentAnimationTime)
                    }
                }
                else -> {
                    //当前位置的单根波形已经绘制完成了，则保持默认高度不变
                    waveHeight = mEnterAnimationDefaultHeight.toFloat()
                    alphaInt = ALPHA_20_PERCENT
                }
            }
            waveStartY = getStartYByHeight(waveHeight)
            waveEndY = getEndYByHeight(waveHeight)
            val newCurrentX = ceil(currentX.toDouble()).toFloat()
            //步骤三：开始绘制矩形
            drawAnimationLine(canvas, alphaInt, newCurrentX, waveStartY.toFloat(), newCurrentX, waveEndY.toFloat())
            currentX += mVirtualAmpGap
        }
        checkAnimationStatus()
        invalidate()
    }

    private fun calculateAnimationTime(viewWidth: Int, itemX: Int, currentX: Float): Float {
        //获取等待执行动画的时间 delayTime
        val delayTime = if (itemX + currentX < mCenterLineX) {
            //绘制屏幕左边的波形
            (viewWidth - currentX) / viewWidth * ENTER_ANIMATION_MOVE_DURATION
        } else {
            //绘制屏幕右边的波形
            (itemX + currentX - mCenterLineX) / mCenterLineX * ENTER_ANIMATION_CHANGE_DURATION
        }
        //当前位置的到执行动画需要延迟的时间，第一个波形则不需要延迟，最后一个波形延迟 animationTotalDuration时长 才执行动画。
        val diffTime = System.currentTimeMillis() - doEnterAnimationTimeMillis
        //当前已经经过的时间和需要等待的时间进行对比,小于0则表示未开始绘制，大于 ENTER_ANIMATION_CHANGE_DURATION 表示某个波形绘制完成
        return diffTime - delayTime
    }

    private fun drawAnimationLine(
        canvas: Canvas,
        alphaInt: Int,
        startX: Float,
        startY: Float,
        stopX: Float,
        stopY: Float,
    ) {
        enterAnimationPaint?.let {
            it.alpha = alphaInt
            canvas.drawLine(startX, startY, stopX, stopY, it)
        }
    }

    private fun checkAnimationStatus() {
        //当前的时间超出了动画总时间，则绘制结束。
        if (System.currentTimeMillis() - doEnterAnimationTimeMillis > ENTER_ANIMATION_MOVE_DURATION + ENTER_ANIMATION_CHANGE_DURATION) {
            mDrawStartAnimationState = ENTER_ANIMATION_WAITING
            mIsDrawStartAnimation = false
        }
    }

    /**
     * draw amplitude wave lines.In order to be compatible with old data, one amplitude draws two or three lines.
     * 绘制波形的方法，区分录制界面(mIsRecordMode = true) 和播放、裁切界面
     * 一：录制界面，波形不能滑动，以中心线分割波形，波形左半边(以LTR来讲)是根据波形数据实时绘制的波形，右半边是未绘制的虚线，波形整体向左移动。
     * 1.一个波形从中心线开始到绘制完成需要  DRAW_AMPLITUDE_DURATION = 450ms时长，从中心线开始边移动边增长高度
     * 2.一个波形在动画期间，高度是根据当前位置距离中心线的距离(移动距离) / 动画时间内移动的总距离(总距离) * 波形的总高度。
     * 3.波形的绘制的时候使用了插值器，高度变化先快后慢。
     * 4.中心线两侧波形的paint颜色不一样，与波形界面一样。
     * 二：波形界面，波形可以滑动，中心线左右两侧都是波形数据，但是paint颜色不一样，区分已播放和未播放。
     * 1.播放界面波形绘制没有动画，一次性绘制完成单个波形。
     * 2.播放界面波形滑动的过程中，会动态刷新波形，使左右两边波形颜色不一样。
     * 3.播放界面拖动seekBar导致波形移动，会动态刷新波形，使左右两边波形颜色不一样。
     * 4.播放界面的数据有两种来源
     * 5.播放界面假波形的高度都是一致的，看起来是很长的一段直线。
     */
    @Suppress("TooGenericExceptionCaught")
    override fun drawAmplitude(canvas: Canvas) {
        if (amplitudes?.isEmpty() != false) {
            return
        }
        //设置动效状态为已经结束状态
        mDrawStartAnimationState = ENTER_ANIMATION_FINISHED
        if (mViewIndex == 0) {
            drawFirstItemAmplitude(canvas)
            return
        }

        drawRecordAmplitude(canvas)
    }

    /**
     * 绘制录制界面波形，有动效。
     * 在 DRAW_AMPLITUDE_DURATION = 600ms内从0达到最大高度。
     * 在动画期间，并且正在录制的情况下，连续绘制波形高度，使波形看起来类似动画增长。
     *
     * @param canvas
     */
    @Suppress("LongMethod", "LoopWithTooManyJumpStatements")
    private fun drawRecordAmplitude(canvas: Canvas) {
        //是否需要执行动画期间的波形,true-需要动态增加高度
        var isAnimationAmp = false
        var amplitueVaule = 0
        var preAmplitueVaule = 0
        var waveStartY = 0
        var waveEndY = 0
        var lineHeight = 0F
        val viewWidth = width
        //用于获取当前item在屏幕中的位置，在绘制波形的时候用于判断绘制的距离和高度
        val location = IntArray(2)
        var isRtl = false
        var amplitueIndex: Int = calculateAmpStartIndex()
        // startX is start draw postion on x orientation of the first item in mRecordAmplitudeList or mPlayAmplitudes.
        val startX: Float = calculateAmpStartX(amplitueIndex.toLong())
        var currentX: Float = startX
        //清理已缓存波形音柱开始绘制的位置
        dmpStartXList.clear()
        while (currentX <= viewWidth) {
            /*
             * 步骤一：绘制准备阶段，获取当前位置的index、前一个波形的index、当前位置的波形实际高度
             * 获取当前item的位置
             */
            if (isSmallWaveType) {
                //小波形图时，获取view的左上角坐标值作为
                location[0] = left
                location[1] = top
            } else {
                getLocationInWindow(location)
            }

            isRtl = isReverseLayout

            /*
             * 画中心线左边的波形
             * 获取上一次的波形数据 preAmplitueVaule
             */
            if (amplitueIndex < getAmplitudeSize()) {
                if (isRtl) {
                    val locationXInScreen = location[0] + viewWidth - currentX
                    if (locationXInScreen < mCenterLineX) {
                        //RTL模式下，当前的x位置在中心线的左侧，则绘制虚线
                        drawDottedLine(canvas, true, currentX, viewWidth)
                        currentX += mVirtualAmpGap
                        continue
                    } else {
                        isAnimationAmp = locationXInScreen <= mCenterLineX + animDistance
                    }
                } else {
                    val locationXInScreen = location[0] + currentX
                    if (locationXInScreen > mCenterLineX) {
                        //LTR模式下，当前的x位置在中心线的右侧，则绘制虚线
                        drawDottedLine(canvas, false, currentX, viewWidth)
                        currentX += mVirtualAmpGap
                        continue
                    } else {
                        isAnimationAmp = locationXInScreen >= mCenterLineX - animDistance
                    }
                }

                amplitueVaule = 0
                //获取当前波形的数据 amplitueVaule
                if (amplitueIndex < getAmplitudeSize()) {
                    preAmplitueVaule = if (amplitueIndex - 1 >= 0) {
                        getAmplitudeItem(amplitueIndex - 1)
                    } else {
                        0
                    }
                    amplitueVaule = getAmplitudeItem(amplitueIndex)
                }
                //tmpIndex++
                amplitueIndex++
            } else {
                //画中心线右边的虚线
                drawDottedLine(canvas, isRtl, currentX, viewWidth)
                currentX += mVirtualAmpGap
                continue
            }
            //步骤二：计算波形rect的上下左右位置的坐标，准备绘制波形矩形
            if (isAnimationAmp) {
                //绘制在做动画期间的波形
                val percent = getCurrentPercent(isRtl, viewWidth, currentX, location[0])
                //传入百分比根据插值器获取当前位置的波形高度百分比（0f-1f）,再成以波形的总高度得到当前的波形高度。
                springInterpolator?.let {
                    lineHeight = it.getInterpolation(percent) * getWaveLineHeight(
                        preAmplitueVaule,
                        amplitueVaule
                    )
                }
            } else {
                //绘制完整波形阶段
                lineHeight = getWaveLineHeight(preAmplitueVaule, amplitueVaule)
            }
            //计算波形rect的顶部和底部y坐标
            waveStartY = getStartYByHeight(lineHeight)
            waveEndY = getEndYByHeight(lineHeight)
            var newCurrentX = currentX
            var newCurrentXLatter = currentX + mAmplitudeWidth
            if (isRtl) {
                //rtl模式下，从右向左绘制
                newCurrentX = viewWidth - currentX
                newCurrentXLatter = newCurrentX - mAmplitudeWidth
            }
            newCurrentXLatter = ceil(newCurrentXLatter.toDouble()).toFloat()
            newCurrentX = ceil(newCurrentX.toDouble()).toFloat()
            var addCurrentX = true
            if (mViewIndex == mTotalCount - 1) {
                if (isRtl) {
                    if (newCurrentXLatter < mCenterLineX) {
                        addCurrentX = false
                        if (mEndItemWidth > 0) {
                            //大于0表示不是初始化的时候调用此方法，是waveRecyclerView真正滑动到最后一个item
                            currentX += mVirtualAmpGap
                        }
                    }
                } else {
                    if (newCurrentXLatter > mEndItemWidth) {
                        addCurrentX = false
                        if (mEndItemWidth > 0) {
                            //大于0表示不是初始化的时候调用此方法，是waveRecyclerView真正滑动到最后一个item
                            currentX += mVirtualAmpGap
                        }
                    }
                }
            }
            /*
             * DebugUtil.i(TAG, "draw amplitude, newCurrentXLatter: " + newCurrentXLatter + ", addCurrentX: " + addCurrentX)
             * 步骤三：开始绘制矩形
             */
            val alphaInt = if (mIsRecording) {
                ALPHA_100_PERCENT
            } else {
                ALPHA_50_PERCENT //录制暂停时，50%透明度置灰效果
            }
            mAmplitudePaint.alpha = alphaInt
            canvas.drawRoundRect(
                newCurrentX, waveStartY.toFloat(), newCurrentXLatter,
                waveEndY.toFloat(), mAmplitudeWidth, mAmplitudeWidth, mAmplitudePaint
            )
            //缓存波形音柱开始绘制的位置
            dmpStartXList.add(newCurrentX)


            if (isEnhance) {
                drawDirectRectBg(isRtl, viewWidth, currentX, canvas)
            }
            //步骤四：一个波形绘制完成，则移动当前位置
            if (addCurrentX) {
                currentX += mVirtualAmpGap
            }
        }

        //如果是正在录制才绘制，暂停录制不绘制。当前的x坐标到中线的距离小于animDistance，则连续绘制
        if (mIsRecording && (mCenterLineX - location[0] - viewWidth <= animDistance)) {
            invalidate()
        }
    }

    /**
     * 绘制定向录音背景色
     */
    private fun drawDirectRectBg(isRtl: Boolean, viewWidth: Int, currentX: Float, canvas: Canvas) {
        var currentBgStartX = currentX
        var currentBgEndX = currentBgStartX + mVirtualAmpGap
        if (isRtl) {
            //rtl模式下，从右向左绘制
            currentBgStartX = viewWidth - currentX
            currentBgEndX = currentBgStartX - mVirtualAmpGap
        }

        currentBgStartX = ceil(currentBgStartX.toDouble()).toFloat()
        currentBgEndX = ceil(currentBgEndX.toDouble()).toFloat()

        if (mViewIndex == mTotalCount - 1) {
            drawEndItemDirectBg(isRtl, currentBgEndX, currentBgStartX, canvas)
        } else {
            drawPlayDirectBg(isRtl, currentBgStartX, currentBgEndX, canvas)
        }
//      DebugUtil.d(TAG, "drawDirectRectBg,index:$mViewIndex,currentBgX:$currentBgStartX,currentBgEndX:$currentBgEndX")
    }

    /**
     * 上一个波形背景的结束X坐标为下一个波形背景的开始X坐标，当item切换时，会有误差，
     * 所以当上一个波形背景结束X坐标不等于当前开始X坐标时，会产生重叠多出1.0f的重复细线，当前X坐标需前移1.0f
     */
    private fun drawPlayDirectBg(
        isRtl: Boolean,
        currentBgStartX: Float,
        currentBgEndX: Float,
        canvas: Canvas
    ) {
        var currentBgStartX1 = currentBgStartX
        var currentBgEndX1 = currentBgEndX
        if (isRtl) {
            if (mPreCurrentBgX != 0f && mPreCurrentBgX != currentBgStartX1) {
                if (mViewIndex > 2 && mPreIndex != mViewIndex) {
                    currentBgEndX1 -= FLOAT_1
                } else {
                    currentBgStartX1 += FLOAT_1
                }
            }
            drawDirectAmpBg(canvas, currentBgStartX1, currentBgEndX1)
    //      DebugUtil.d(TAG, "drawDirectRectBg rtl,index:$mViewIndex,currentBgX:$currentBgStartX,currentBgEndX:$currentBgEndX")
        } else {
            if (mViewIndex > NUM_TWO && mPreCurrentBgX != 0f && mPreCurrentBgX != currentBgStartX1) {
                currentBgStartX1 += FLOAT_1
            }
            drawDirectAmpBg(canvas, currentBgStartX1, currentBgEndX1)
        }
    }

    private fun drawEndItemDirectBg(
        isRtl: Boolean,
        currentBgEndX: Float,
        currentBgStartX: Float,
        canvas: Canvas
    ) {
        var currentBgStartX1 = currentBgStartX
        if (isRtl) {
            //rtl时，currentBgEndX大于中心线才进行绘制
            if (currentBgEndX > mCenterLineX) {
                if (mPreCurrentBgX != 0f && mPreCurrentBgX != currentBgStartX1) {
                    currentBgStartX1 += FLOAT_1
                }
                drawDirectAmpBg(canvas, currentBgStartX1, currentBgEndX)
    //                  DebugUtil.d(TAG, "drawDirectRectBg isRtl endCount,index:$mViewIndex,currentBgX:$currentBgStartX,currentBgEndX:$currentBgEndX")
            }
        } else {
            //ltr时，currentBgEndX小于等于中心线才进行绘制
            if (currentBgEndX <= mEndItemWidth) {
                if (mViewIndex > 2 && mPreCurrentBgX != 0f && mPreCurrentBgX != currentBgStartX1) {
                    currentBgStartX1 += FLOAT_1
                }
                drawDirectAmpBg(canvas, currentBgStartX1, currentBgEndX)
            }
        }
    }

    private fun drawDirectAmpBg(
        canvas: Canvas,
        currentBgStartX: Float,
        currentBgEndX: Float
    ) {
        mBackgroundPaint?.let {
            canvas.drawRect(
                currentBgStartX, mMarkViewHeight.toFloat(), currentBgEndX,
                mViewHeight, it
            )
        }
        mPreCurrentBgX = currentBgEndX
        mPreIndex = mViewIndex
    }

    @Suppress("TooGenericExceptionCaught")
    private fun getAmplitudeItem(index: Int): Int {
        return try {
            amplitudes[index]
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getAmplitudeItem error ", e)
            0
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun getAmplitudeSize(): Int {
        return try {
            amplitudes?.size ?: 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getAmplitudeItem error ", e)
            0
        }
    }

    private fun getCurrentPercent(isRtl: Boolean, viewWidth: Int, currentX: Float, locationX: Int): Float {
        var percent = if (isRtl) {
            (locationX + viewWidth - currentX - mCenterLineX) / animDistance
        } else {
            (mCenterLineX - locationX - currentX) / animDistance
        }
        if (percent < 0) {
            percent = 0f
        }
        if (percent > 1) {
            percent = 1f
        }

        return percent
    }
}