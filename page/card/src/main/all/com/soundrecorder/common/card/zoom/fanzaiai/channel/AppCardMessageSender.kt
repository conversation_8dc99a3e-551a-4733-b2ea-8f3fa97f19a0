/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AppCardMessageSender.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel

import android.os.Handler
import android.os.HandlerThread
import com.google.gson.GsonBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import org.hapjs.features.channel.ChannelMessage
import org.hapjs.features.channel.IHapChannel
import org.json.JSONObject

object AppCardMessageSender {

    const val TAG = "AppCardMessageSender"

    private const val DELAY_TIME = 5000L

    @Volatile
    private var mIHapChannel: IHapChannel? = null

    private var mWorkHandler: Handler

    private var currentInfo: AudioFileInfo? = null

    private val delayTask = Runnable {
        currentInfo?.let {
            DebugUtil.d(TAG, "run delay task displayName=${it.displayName}")
            AppCardStateProcessor.updateCard(BaseApplication.getAppContext(), false, it)
        }
    }

    init {
        val handlerThread = HandlerThread(TAG)
        handlerThread.start()
        mWorkHandler = Handler(handlerThread.looper)
        DebugUtil.d(TAG, "init mWorkHandler=$mWorkHandler")
    }

    fun cancelDelayTask() {
        mWorkHandler.removeCallbacks(delayTask)
    }

    fun senDelayNormalMessage(audioFileInfo: AudioFileInfo) {
        this.currentInfo = audioFileInfo
        delayBackToNormal()
    }

    private fun delayBackToNormal() {
        mWorkHandler.removeCallbacks(delayTask)
        mWorkHandler.postDelayed(delayTask, DELAY_TIME)
    }

    fun sendMessage(audioFileInfo: AudioFileInfo): Boolean {
        runCatching {
            return if (isChannelEnable()) {
                currentInfo = audioFileInfo
                val gson = GsonBuilder().disableHtmlEscaping().create()
                val channelMessage = ChannelMessage()
                val toJson = gson.toJson(audioFileInfo)
                channelMessage.setData(toJson)
                DebugUtil.d(
                    TAG,
                    "sendMessage displayName:${audioFileInfo.displayName},mediaId=${audioFileInfo.mediaId}," +
                            "status=${audioFileInfo.status}"
                )
                sendMessage(channelMessage)
                true
            } else {
                false
            }
        }.onFailure {
            DebugUtil.e(TAG, "sendMessage audio error $it")
        }
        return false
    }

    fun sendErrorMessage(errorType: Int, errorMessage: String): Boolean {
        if (isChannelEnable()) {
            val channelMessage = ChannelMessage()
            val obj = JSONObject()
            obj.put(KEY_STATUS, errorType)
            obj.put(KEY_SUMMARY_TEXT_ERROR_MESSAGE, errorMessage)
            channelMessage.setData(obj.toString())
            DebugUtil.d(TAG, "message json :$obj")
            sendMessage(channelMessage)
            delayBackToNormal()
            return true
        }
        return false
    }

    fun sendProgress(status: Int, progress: String) {
        if (isChannelEnable()) {
            val channelMessage = ChannelMessage()
            val obj = JSONObject()
            obj.put(KEY_STATUS, status)
            obj.put(KEY_PROGRESS, progress)
            channelMessage.setData(obj.toString())
            DebugUtil.d(TAG, "sendProgress json = $obj")
            sendMessage(channelMessage)
        }
    }

    fun sendCheckResult(exist: Boolean, jumpUri: String) {
        if (isChannelEnable()) {
            val channelMessage = ChannelMessage()
            val obj = JSONObject()
            obj.put(KEY_STATUS, STATUS_CHECK_SUMMARY_RESULT)
            obj.put(KEY_CHECK_FILE_RESULT, exist)
            obj.put(KEY_JUMP_URI, jumpUri)
            channelMessage.setData(obj.toString())
            DebugUtil.d(TAG, "sendCheckFile obj=$obj")
            sendMessage(channelMessage)
        }
    }

    fun isChannelEnable(): Boolean {
        val enable = mIHapChannel?.status == CustomChannelHandler.CHANNEL_STATUS_OPEN
        DebugUtil.d(
            TAG,
            " isChannelEnable enable=$enable mIHapChannel?.status ${mIHapChannel?.status}"
        )
        return enable
    }

    private fun sendMessage(channelMessage: ChannelMessage) {
        runCatching {
            mIHapChannel?.send(channelMessage, null)
            DebugUtil.i(TAG, "sendMessage mIHapChannel=$mIHapChannel")
        }.onFailure {
            DebugUtil.e(TAG, "sendMessage channel error $it")
        }
    }

    fun updateChannel(channel: IHapChannel) {
        DebugUtil.d(TAG, "updateChannel status=${channel.status}")
        mIHapChannel = channel
    }

    fun onChannelClosed() {
        DebugUtil.d(TAG, "onChannelClosed")
    }

    fun executeRunnable(runnableFunc: () -> Unit) {
        mWorkHandler.post {
            runnableFunc.invoke()
        }
    }
}