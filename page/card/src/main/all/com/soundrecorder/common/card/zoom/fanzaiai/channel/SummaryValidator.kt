/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryValidator.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.card.R
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE_WARN
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_SMALL
import com.soundrecorder.modulerouter.summary.ERROR_CODE_FORMAT
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SIZE_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUCCESS
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUPER_SAVE_MODE
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper.smartNameAction

object SummaryValidator {
    /*摘要支持最大文件size*/
    private const val MAX_SUPPORT_RECORD_SIZE = 500 * 1024 * 1024

    /*摘要支持最大警告文件时长：2小时*/
    private const val MAX_SUPPORT_RECORD_DURATION_WARN = 2 * 60 * 60 * 1000

    /*摘要支持最大文件时长：5小时*/
    private const val MAX_SUPPORT_RECORD_DURATION = 5 * 60 * 60 * 1000

    /*摘要支持最小文件时长*/
    private const val MIN_SUPPORT_RECORD_DURATION = 20 * 1000

    /*摘要支持最大时长*/
    private const val SUMMARY_MAX_DURATION_HOUR = 5
    private const val TAG = "SummaryConditionChecker"

    private const val SUMMARY_MAX_DURATION_NEW = 2
    private var unifiedSummaryManager: IUnifiedSummaryCallBack? = null

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    object UIStatusCode {
        //停止ASR。摘要生成过程中，手动中断生成，部分内容已保存至“便签”的情况。
        const val UI_ASR_ABORT_STREAM = -107
    }

    object SummaryStatusCode {
        // exception code.
        const val SUMMARY_EXCEPTION_CODE = -1
        // The code of summaryErrorCode is generated normally and the process is closed.
        const val SUMMARY_NORMAL_CODE = 0
    }

    @JvmStatic
    fun checkSummaryPreCondition(context: Context, record: AudioFileInfo): Pair<Int, String> {
        DebugUtil.d(TAG, "checkSummaryPreCondition $record")
        if (RecordRouterManager.instance?.isSuperPowerSaveModeState(context) == true) { // 超级省电模式
            return Pair(
                ERROR_CODE_SUPER_SAVE_MODE,
                context.getString(R.string.summary_error_super_power_mode)
            )
        }

        if (!isFileFormatMet(record.mimeType)) { // 格式不支持
            return Pair(
                ERROR_CODE_FORMAT,
                context.getString(R.string.summary_error_record_format_not_support)
            )
        }
        if (!isDamageFileDuration(record.mDuration)) { // 时长<=0
            return Pair(
                ERROR_CODE_DURATION_SMALL,
                context.getString(R.string.summary_error_damage_file)
            )
        }

        if (!isFileDurationMinMet(record.mDuration)) { // 时长<=20s
            return Pair(
                ERROR_CODE_DURATION_SMALL,
                context.getString(R.string.summary_error_record_short)
            )
        }

        if (!isFileSizeMaxMet(record.size)) { // 大于500M
            return Pair(
                ERROR_CODE_SIZE_LARGE,
                context.getString(R.string.toast_summary_error_size_over)
            )
        }

        if (!isFileDurationMaxMet(record.mDuration)) { //大于5小时
            return Pair(
                ERROR_CODE_DURATION_LARGE, context.resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.summary_error_record_long,
                    SUMMARY_MAX_DURATION_HOUR,
                    SUMMARY_MAX_DURATION_HOUR
                )
            )
        }

        if (!isFileDurationMaxWarnMet(record.mDuration)) { // 大于2小时
            return Pair(
                ERROR_CODE_DURATION_LARGE_WARN, context.resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.summary_error_record_long,
                    SUMMARY_MAX_DURATION_NEW,
                    SUMMARY_MAX_DURATION_NEW
                )
            )
        }
        return Pair(ERROR_CODE_SUCCESS, "SUCCESS")
    }

    /**
     * 校验文件格式mimeType
     */
    @JvmStatic
    fun isFileFormatMet(fileFormat: String): Boolean {
        return when (fileFormat) {
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_3GPP,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB,
            RecordConstant.MIMETYPE_WAV,
            RecordConstant.MIMETYPE_ACC,
            RecordConstant.MIMETYPE_ACC_ADTS,
            -> true

            else -> false
        }
    }

    /**
     * 文件大小是否满足最大限制
     */
    @JvmStatic
    fun isFileSizeMaxMet(fileSize: Long): Boolean {
        return fileSize < MAX_SUPPORT_RECORD_SIZE
    }

    /**
     * 文件时长是否满足最小限制
     */
    @JvmStatic
    fun isFileDurationMinMet(fileDuration: Long): Boolean {
        return fileDuration > MIN_SUPPORT_RECORD_DURATION
    }

    @JvmStatic
    fun isDamageFileDuration(fileDuration: Long): Boolean {
        return fileDuration > 0
    }

    /**
     * 文件时长是否满足最大限制警告
     */
    @JvmStatic
    fun isFileDurationMaxWarnMet(fileDuration: Long): Boolean {
        return fileDuration <= MAX_SUPPORT_RECORD_DURATION_WARN
    }

    /**
     * 文件时长是否满足最大限制
     */
    @JvmStatic
    fun isFileDurationMaxMet(fileDuration: Long): Boolean {
        return fileDuration <= MAX_SUPPORT_RECORD_DURATION
    }

    /**
     * AI summaries are supported, and the plugin has been downloaded.
     */
    fun isNeedStartAISummary(callback: (Boolean) -> Unit) {
        DebugUtil.d(
            TAG,
            "isNeedStartAISummary summaryApi?.isDeviceSupportAIUnit():${summaryApi?.isDeviceSupportAIUnit(BaseApplication.getAppContext())}"
        )
        if (summaryApi?.isDeviceSupportAIUnit(BaseApplication.getAppContext()) == true) {
            checkDownloadAIUnitPlugins(callback)
        } else {
            callback(false)
        }
    }

    fun checkDownloadAIUnitPlugins(callback: (Boolean) -> Unit) {
        if (unifiedSummaryManager == null) {
            unifiedSummaryManager = smartNameAction?.newUnifiedSummaryManager()
        }
        unifiedSummaryManager?.showAiUnitPluginsDialog(
            BaseApplication.getAppContext(),
            object : IPluginDownloadCallback {
                override fun onDownLoadResult(result: Boolean) {
                    callback(result)
                }
            },
            isSummary = true
        )
    }

    /**
     * Check whether the note exists
     */
    fun isNoteExist(
        noteData: NoteData? = null
    ): Boolean {
        var isNoteExist = noteData != null
        noteData?.noteId?.let {
            isNoteExist = browseFileApi?.isNoteExist(it) == true
        }
        return isNoteExist
    }
}