/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AudioFileInfo.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel

data class AudioFileInfo(
    var displayName: String = "",
    var title: String = "",
    var data: String = "",
    var relativePath: String = "",
    var dateModified: Long = 0,
    var dateAdd: Long = 0,
    var mediaId: Long = -1L,
    var mDuration: Long = 0,
    var size: Long = 0,
    var mimeType: String = "",
    var ownerPackageName: String? = null,
    var time: Long = 0,
    /**
     * 状态
     */
    var status: Int = STATUS_CONVERT_TEXT,
    var uri: String = "",
    var errorMessage: String = "",
    var typeName: String = "",
    var progress: Int = 0
) {
    fun reset() {
        displayName = ""
        title = ""
        data = ""
        relativePath = ""
        dateModified = 0L
        dateAdd = 0L
        mediaId = -1L
        mDuration = 0L
        size = 0L
        mimeType = ""
        ownerPackageName = null
        time = 0L
        status = STATUS_CONVERT_TEXT
        uri = ""
        errorMessage = ""
        typeName = ""
    }

    fun setError(errorMessage: String) {
        this.errorMessage = errorMessage
        status = STATUS_SUMMARY_ERROR_INFO
    }

    /**
     * 除time属性，判断是否相同。
     */
    fun equalsExcludeTime(newAudio: AudioFileInfo): Boolean {
        if (this === newAudio) return true // 如果是同一个对象，直接返回 true
        return displayName == newAudio.displayName
                && title == newAudio.title
                && data == newAudio.data
                && relativePath == newAudio.relativePath
                && dateModified == newAudio.dateModified
                && mediaId == newAudio.mediaId
                && mDuration == newAudio.mDuration
                && size == newAudio.size
                && mimeType == newAudio.mimeType
                && ownerPackageName == newAudio.ownerPackageName
                && status == newAudio.status
                && uri == newAudio.uri
                && errorMessage == newAudio.errorMessage
                && typeName == newAudio.typeName
                && progress == newAudio.progress
    }
}

/**
 * 通用转摘要异常信息
 */
const val STATUS_SUMMARY_ERROR_INFO = 2000

/**
 * 通用转文本异常信息
 */
const val STATUS_CONVERT_ERROR_INFO = 3000

/**
 * 状态：转摘要
 */
const val STATUS_CONVERT_SUMMARY = 2001

/**
 * 状态：转文本
 */
const val STATUS_CONVERT_TEXT = 2002

/**
 * 状态：查看摘要
 */
const val STATUS_LOOK_SUMMARY = 2003

/**
 * 状态：查看文本
 */
const val STATUS_LOOK_TEXT = 2004

/**
 * 开始转摘要
 */
const val STATUS_CONVERT_SUMMARY_IN_PROGRESS = 2005

/**
 * 开始转文本
 */
const val STATUS_CONVERT_TEXT_IN_PROGRESS = 2006

/**
 * 检查摘要的结果
 */
const val STATUS_CHECK_SUMMARY_RESULT = 2007

/**
 * 生成摘要失败默认提示
 */
const val STATUS_DEFAULT_SUMMARY_ERROR = 3001

/**
 * 状态key
 */
const val KEY_STATUS = "status"

/**
 * 异常信息
 */
const val KEY_SUMMARY_TEXT_ERROR_MESSAGE = "errorMessage"

/**
 * 进度条
 */
const val KEY_PROGRESS = "progress"

/**
 * 检查文件的结果
 */
const val KEY_CHECK_FILE_RESULT = "checkFileResult"

/**
 * 跳转的uri
 */
const val KEY_JUMP_URI = "uri"

