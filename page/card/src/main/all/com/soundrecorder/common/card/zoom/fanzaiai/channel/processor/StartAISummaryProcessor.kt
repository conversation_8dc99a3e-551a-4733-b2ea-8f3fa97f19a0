/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File       : StartAISummaryProcessor.kt
 ** Description: StartAISummaryProcessor.kt
 ** Version    : 1.0
 ** Date       : 2025/06/23
 ** Author     : <EMAIL>
 * <p>
 ** ---------------------Revision History: ---------------------
 **  <author>   <data>   <version >    <desc>
 **  W9017070  2025/06/23      1.0     create file
 ****************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel.processor

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.card.zoom.fanzaiai.channel.SummaryValidator
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object StartAISummaryProcessor {
    private const val TAG = "StartAISummaryProcessor"

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    fun aiSummaryProcessor(
        mediaId: Long,
        recordType: Int,
        recordTime: Long,
        startCallBack: () -> Unit,
        errorCallBack: () -> Unit,
        endCallBack: () -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            //check if ai summary isExist?
            val isExist = summaryApi?.getAISummaryIsExists(mediaId)
            if (isExist == true) {
                DebugUtil.d(TAG, "AISummaryProcessor isExist = $isExist")
                return@launch
            }

            //check if note is exist summary in note isExist
            val noteData = NoteDbUtils.queryNoteByMediaId(mediaId.toString())
            val bNoteExist = SummaryValidator.isNoteExist(noteData)
            if (bNoteExist == true) {
                DebugUtil.d(TAG, "AISummaryProcessor bNoteExist = $isExist")
                return@launch
            }
            summaryApi?.startAISummaryForCard(
                mediaId, recordType, recordTime, startCallBack,
                errorCallBack, endCallBack
            )
        }
    }
}