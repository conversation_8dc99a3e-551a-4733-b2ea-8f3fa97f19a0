/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordFileDragHelper
 * Description:
 * Version: 1.0
 * Date: 2023/12/20
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/20 1.0 create
 */

package com.soundrecorder.browsefile.drag

import android.content.ClipData
import android.content.ClipDescription
import android.content.Context
import android.net.Uri
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.drag.view.DragShadowView
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.MediaDBUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object RecordFileDragHelper {
    const val DRAG_LOCAL_STATE = "drag_from_recorder"
    private const val DRAG_LABEL = "Recorder"
    private const val DRAG_FLAG = View.DRAG_FLAG_GLOBAL or View.DRAG_FLAG_OPAQUE or View.DRAG_FLAG_GLOBAL_URI_READ
    private const val MAX_DRAG_COUNT = 99
    private const val TAG = "RecordFileDragHelper"

    @JvmStatic
    fun startDragAndDrop(context: Context, dragView: View, list: List<Record>, resultCallback: (result: Boolean) -> Unit) {
        DebugUtil.i(TAG, "startDragAndDrop size=${list.size}")
        (context as? AppCompatActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
            runCatching {
                createClipData(list)?.let {
                    val dragShadowView = createDragShadow(dragView, it.itemCount, true)
                    val result = dragView.startDragAndDrop(it, dragShadowView, DRAG_LOCAL_STATE, DRAG_FLAG)
                    resultCallback.invoke(result)
                    DebugUtil.i(TAG, "startDragAndDrop clipData.itemCount=${it.itemCount}, result=$result")
                }
            }.onFailure {
                DebugUtil.e(TAG, "startDragAndDrop error", it)
            }
        }
    }

    @JvmStatic
    fun checkCanDragAndDrop(context: Context, list: List<Record>?): Boolean {
        if (list.isNullOrEmpty()) {
            DebugUtil.d(TAG, "checkCanDragAndDrop false list is null or empty")
            return false
        }
        if (list.size > MAX_DRAG_COUNT) {
            DebugUtil.d(TAG, "checkCanDragAndDrop false list over max count")
            ToastManager.showShortToast(
                context,
                context.resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.tip_file_drag_max_count,
                    MAX_DRAG_COUNT,
                    MAX_DRAG_COUNT
                )
            )
            return false
        }
        return true
    }

    /**
     * 构造ClipData
     */
    @JvmStatic
    fun createClipData(list: List<Record>): ClipData? {
        var dragClipData: ClipData? = null
        var dataUri: Uri
        list.forEach { record ->
            dataUri = MediaDBUtils.genUri(record.id)
            if (dragClipData == null) {
                dragClipData = ClipData(
                    DRAG_LABEL, arrayOf(ClipDescription.MIMETYPE_TEXT_PLAIN), ClipData.Item(dataUri))
            } else {
                dragClipData?.addItem(ClipData.Item(dataUri))
            }
        }
        return dragClipData
    }

    /**
     * 构造ShadowView
     */
    @JvmStatic
    fun createDragShadow(view: View, size: Int, hasRadius: Boolean): DragShadowView {
        return DragShadowView(view, size, hasRadius)
    }
}