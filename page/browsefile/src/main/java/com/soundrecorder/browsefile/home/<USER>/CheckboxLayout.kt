/***********************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.soundrecorder.browsefile.home.view

import android.animation.*
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.PathInterpolator
import android.widget.Checkable
import androidx.core.view.isVisible
import com.coui.appcompat.checkbox.COUICheckBox
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.widget.ClickScaleCardView

class CheckboxLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ClickScaleCardView(context, attrs, defStyleAttr), Checkable {

    companion object {
        const val TAG = "CheckboxLayout"
    }

    private var mCheckBox: COUICheckBox? = null

    private var enterAnim: Animator? = null
    private var exitAnim: Animator? = null

    override fun onFinishInflate() {
        super.onFinishInflate()
        mCheckBox = findViewById<View>(R.id.checkbox) as? COUICheckBox
    }

    override fun isChecked(): Boolean {
        return mCheckBox?.state in arrayOf(COUICheckBox.SELECT_ALL, COUICheckBox.SELECT_PART)
    }

    override fun setChecked(isChecked: Boolean) {
        if (isChecked) {
            mCheckBox?.state = COUICheckBox.SELECT_ALL
        } else {
            mCheckBox?.state = COUICheckBox.SELECT_NONE
        }
    }

    override fun toggle() {
        mCheckBox?.toggle()
    }

    fun isCheckBoxNotCompleteVisible(): Boolean {
        return mCheckBox?.isVisible != true || mCheckBox?.alpha != 1f
    }

    fun setEditModeState() {
        mCheckBox?.let {
            it.visibility = View.VISIBLE
            it.alpha = 1f
        }
    }

    fun setNormalModeState() {
        mCheckBox?.let {
            it.visibility = View.GONE
            it.alpha = 0f
        }
    }

    fun startOrContinueEditModeEnterAnimation(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (enterAnim?.isRunning == true) {
            DebugUtil.i(TAG, "startOrContinueEditModeEnterAnimation, enter anim is running")
            return
        }

        cancelEditModeExitAnim()
        continueEditModeEnterAnimation(extraAnimator, animatorListener)
    }

    private fun continueEditModeEnterAnimation(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (mCheckBox == null) {
            return
        }

        val checkboxEnterAnim =
            ObjectAnimator.ofFloat(mCheckBox!!, "alpha", mCheckBox!!.alpha, 1f).apply {
                startDelay = 167
                duration = 180
                interpolator = PathInterpolatorHelper.couiEaseInterpolator
            }

        val animatorList = mutableListOf<Animator>().also {
            if (!extraAnimator.isNullOrEmpty()) {
                it.addAll(extraAnimator)
            }
            it.add(checkboxEnterAnim)
        }
        enterAnim = AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    animatorListener?.onAnimationStart(animation)
                    mCheckBox!!.visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator) {
                    animatorListener?.onAnimationEnd(animation)
                    mCheckBox!!.visibility = View.VISIBLE
                    animation?.removeListener(this)
                    enterAnim = null
                }
            })
            it.start()
        }
    }

    fun startOrContinueEditModeExitAnimation(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (exitAnim?.isRunning == true) {
            DebugUtil.i(TAG, "startOrContinueEditModeEnterAnimation, exit anim is running")
            return
        }

        cancelEditModeEnterAnim()
        continueEditModeExitAnimation(extraAnimator, animatorListener)
    }

    private fun continueEditModeExitAnimation(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (mCheckBox == null) {
            return
        }

        val checkboxExitAnim = ObjectAnimator.ofFloat(mCheckBox!!, "alpha", mCheckBox!!.alpha, 0f).apply {
            duration = 180
            interpolator = PathInterpolatorHelper.couiEaseInterpolator
        }

        val animatorList = mutableListOf<Animator>().also {
            if (!extraAnimator.isNullOrEmpty()) {
                it.addAll(extraAnimator)
            }
            it.add(checkboxExitAnim)
        }
        exitAnim = AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {

                override fun onAnimationStart(animation: Animator) {
                    animatorListener?.onAnimationStart(animation)
                    mCheckBox!!.visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator) {
                    animatorListener?.onAnimationEnd(animation)
                    mCheckBox!!.visibility = View.GONE
                    animation?.removeListener(this)
                    exitAnim = null
                }
            })
            it.start()
        }
    }

    fun cancelAnimation() {
        cancelEditModeEnterAnim()
        cancelEditModeExitAnim()
    }

    private fun cancelEditModeEnterAnim() {
        enterAnim?.cancel()
        enterAnim = null
    }

    private fun cancelEditModeExitAnim() {
        exitAnim?.cancel()
        exitAnim = null
    }

    override fun onRelease() {
        super.onRelease()
        cancelAnimation()
    }
}